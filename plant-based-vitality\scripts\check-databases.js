const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function checkDatabases() {
  try {
    console.log('🔍 Connecting to check databases...');
    const client = await pool.connect();
    console.log('✅ Connected successfully!');
    
    // Show current database and user
    const dbResult = await client.query('SELECT current_database(), current_user');
    console.log(`📍 Current database: ${dbResult.rows[0].current_database}`);
    console.log(`👤 Current user: ${dbResult.rows[0].current_user}`);
    
    // List all databases the user has access to
    console.log('\n📋 Available databases:');
    try {
      const dbsResult = await client.query('SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname');
      dbsResult.rows.forEach(row => {
        console.log(`   - ${row.datname}`);
      });
    } catch (error) {
      console.log('   Could not list databases:', error.message);
    }
    
    // Check if we can create tables in current database
    console.log('\n🔧 Testing table creation permissions...');
    try {
      await client.query('CREATE TABLE IF NOT EXISTS test_permissions (id SERIAL PRIMARY KEY, test_col TEXT)');
      console.log('✅ Can create tables in current database');
      
      // Clean up test table
      await client.query('DROP TABLE IF EXISTS test_permissions');
      console.log('✅ Test table cleaned up');
    } catch (error) {
      console.log('❌ Cannot create tables:', error.message);
    }
    
    client.release();
    console.log('\n✅ Database check completed');
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await pool.end();
  }
}

checkDatabases();
