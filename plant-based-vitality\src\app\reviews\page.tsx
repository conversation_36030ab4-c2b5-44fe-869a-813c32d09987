import { Metadata } from 'next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StarIcon } from '@heroicons/react/24/solid';
import { sampleProducts } from '@/data/products';
import { formatPrice } from '@/lib/utils';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'Moringa Supplement Reviews - Plant Based Vitality',
  description: 'Expert reviews of the best moringa supplements. Independent testing, detailed analysis, and honest recommendations for moringa powder, capsules, and tea.',
  keywords: ['moringa reviews', 'moringa supplement reviews', 'best moringa products', 'moringa powder reviews'],
};

export default function ReviewsPage() {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Moringa Supplement Reviews
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Expert-tested reviews of the best moringa supplements. Independent analysis, 
            third-party testing, and honest recommendations to help you choose the right product.
          </p>
        </header>

        {/* Reviews Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {sampleProducts.map((product) => (
            <Card key={product.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="aspect-video relative mb-4">
                  <Image
                    src={product.images[0]?.url || '/images/placeholder.svg'}
                    alt={product.images[0]?.alt || product.name}
                    fill
                    className="object-cover rounded-lg"
                  />
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-muted-foreground uppercase">
                    {product.type}
                  </span>
                  <div className="flex items-center gap-1">
                    <div className="flex">{renderStars(product.rating)}</div>
                    <span className="text-sm font-medium">{product.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-xl">{product.name}</CardTitle>
                <CardDescription>
                  by {product.brand} • {formatPrice(product.price)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 mb-4">
                  <div className="flex justify-between text-sm">
                    <span>Quality</span>
                    <span className="font-medium">
                      {product.rating >= 4.5 ? 'Excellent' : 
                       product.rating >= 4.0 ? 'Very Good' : 'Good'}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Type</span>
                    <span className="font-medium capitalize">{product.type}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Price</span>
                    <span className="font-medium">{formatPrice(product.price)}</span>
                  </div>
                </div>

                {/* Quality Indicators */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {product.organic && (
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      Organic
                    </span>
                  )}
                  {product.thirdPartyTested && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Tested
                    </span>
                  )}
                  {product.madeInUSA && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                      USA
                    </span>
                  )}
                </div>

                <Link href={`/reviews/${product.id}`}>
                  <Button className="w-full">Read Full Review</Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-muted/50 rounded-lg p-8">
          <h2 className="text-2xl font-bold mb-4">
            Looking for Our Top Recommendations?
          </h2>
          <p className="text-muted-foreground mb-6">
            Check out our comprehensive ranking of the best moringa supplements of 2025.
          </p>
          <Link href="/best-moringa-supplements">
            <Button size="lg">View Top 5 Rankings</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
