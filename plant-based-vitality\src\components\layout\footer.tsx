import Link from 'next/link';

const navigation = {
  reviews: [
    { name: 'Best Moringa Powder', href: '/reviews/best-moringa-powder' },
    { name: 'Best Moringa Capsules', href: '/reviews/best-moringa-capsules' },
    { name: 'Organic Moringa Reviews', href: '/reviews/organic-moringa' },
    { name: 'All Reviews', href: '/reviews' },
  ],
  guides: [
    { name: 'What is Moringa?', href: '/guides/what-is-moringa' },
    { name: 'Moringa Dosage Guide', href: '/guides/moringa-dosage' },
    { name: 'Moringa Benefits', href: '/guides/moringa-benefits' },
    { name: 'Buying Guide', href: '/guides/buying-guide' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Our Process', href: '/our-process' },
    { name: 'Contact', href: '/contact' },
    { name: 'Privacy Policy', href: '/privacy' },
  ],
  legal: [
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Affiliate Disclosure', href: '/affiliate-disclosure' },
    { name: 'Medical Disclaimer', href: '/medical-disclaimer' },
    { name: 'Cookie Policy', href: '/cookies' },
  ],
};

export function Footer() {
  return (
    <footer className="bg-muted/50 border-t border-border/40">
      <div className="mx-auto max-w-7xl px-6 py-16 sm:py-24 lg:px-8 lg:py-32">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">PBV</span>
              </div>
              <span className="text-xl font-bold text-foreground">
                Plant Based Vitality
              </span>
            </div>
            <p className="text-sm leading-6 text-muted-foreground max-w-md">
              America's most trusted source for Moringa supplement reviews. We provide 
              science-backed analysis and independent testing to help you make informed 
              decisions about your health.
            </p>
            <div className="text-xs text-muted-foreground">
              <p className="mb-2">
                <strong>Medical Disclaimer:</strong> This website is for informational purposes only. 
                Consult your healthcare provider before starting any supplement regimen.
              </p>
              <p>
                <strong>Affiliate Disclosure:</strong> We may earn commissions from qualifying purchases 
                made through our affiliate links. This doesn't affect our editorial independence.
              </p>
            </div>
          </div>
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-foreground">Reviews</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.reviews.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-foreground">Guides</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.guides.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-foreground">Company</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.company.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-foreground">Legal</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.legal.map((item) => (
                    <li key={item.name}>
                      <Link 
                        href={item.href} 
                        className="text-sm leading-6 text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-16 border-t border-border/40 pt-8 sm:mt-20 lg:mt-24">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <p className="text-xs leading-5 text-muted-foreground">
              &copy; 2025 Plant Based Vitality. All rights reserved.
            </p>
            <p className="mt-4 text-xs leading-5 text-muted-foreground sm:mt-0">
              Last updated: January 2025
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
