{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format price to USD currency\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\n/**\n * Generate slug from string\n */\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.substring(0, length).trim() + '...';\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Generate star rating display\n */\nexport function generateStarRating(rating: number): string {\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 !== 0;\n  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);\n\n  return (\n    '★'.repeat(fullStars) +\n    (hasHalfStar ? '☆' : '') +\n    '☆'.repeat(emptyStars)\n  );\n}\n\n/**\n * Validate email address\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate affiliate link with tracking parameters\n */\nexport function generateAffiliateLink(\n  baseUrl: string,\n  productId: string,\n  source?: string\n): string {\n  const url = new URL(baseUrl);\n  url.searchParams.set('utm_source', 'plantbasedvitality');\n  url.searchParams.set('utm_medium', 'affiliate');\n  url.searchParams.set('utm_campaign', productId);\n  if (source) {\n    url.searchParams.set('utm_content', source);\n  }\n  return url.toString();\n}\n\n/**\n * Hash IP address for privacy\n */\nexport function hashIP(ip: string): string {\n  // Simple hash function for privacy compliance\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n/**\n * Get reading time estimate\n */\nexport function getReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = text.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAKO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,QAAQ,IAAI,KAAK;AAC5C;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,SAAS,MAAM;IACnC,MAAM,aAAa,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;IAEvD,OACE,IAAI,MAAM,CAAC,aACX,CAAC,cAAc,MAAM,EAAE,IACvB,IAAI,MAAM,CAAC;AAEf;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,sBACd,OAAe,EACf,SAAiB,EACjB,MAAe;IAEf,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB;IACrC,IAAI,QAAQ;QACV,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe;IACtC;IACA,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,OAAO,EAAU;IAC/B,8CAA8C;IAC9C,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAKO,SAAS,eAAe,IAAY;IACzC,MAAM,iBAAiB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM;IAC1C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const baseStyles = cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n      {\n        // Variants\n        'bg-primary text-primary-foreground hover:bg-primary/90':\n          variant === 'default',\n        'bg-secondary text-secondary-foreground hover:bg-secondary/80':\n          variant === 'secondary',\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground':\n          variant === 'outline',\n        'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n        'text-primary underline-offset-4 hover:underline': variant === 'link',\n      },\n      {\n        // Sizes\n        'h-10 px-4 py-2': size === 'default',\n        'h-9 rounded-md px-3': size === 'sm',\n        'h-11 rounded-md px-8': size === 'lg',\n        'h-10 w-10': size === 'icon',\n      },\n      className\n    );\n\n    return <button className={baseStyles} ref={ref} {...props} />;\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,mQACA;QACE,WAAW;QACX,0DACE,YAAY;QACd,gEACE,YAAY;QACd,kFACE,YAAY;QACd,gDAAgD,YAAY;QAC5D,mDAAmD,YAAY;IACjE,GACA;QACE,QAAQ;QACR,kBAAkB,SAAS;QAC3B,uBAAuB,SAAS;QAChC,wBAAwB,SAAS;QACjC,aAAa,SAAS;IACxB,GACA;IAGF,qBAAO,8OAAC;QAAO,WAAW;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAC3D;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/data/products.ts"], "sourcesContent": ["import { Product } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: 'organic-moringa-powder-premium',\n    name: 'Organic Moringa Powder Premium',\n    brand: 'Pure Moringa',\n    type: 'powder',\n    price: 24.99,\n    rating: 4.8,\n    pros: [\n      'USDA Organic certified',\n      'Third-party tested for purity',\n      'Rich, earthy flavor',\n      'Fine powder texture mixes well',\n      'Excellent nutrient profile',\n      'Sustainable farming practices'\n    ],\n    cons: [\n      'Slightly higher price point',\n      'Strong taste may not appeal to everyone',\n      'Packaging could be more eco-friendly'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example1',\n        price: 24.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'vitacost-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Vitacost',\n        url: 'https://vitacost.com/example1',\n        price: 26.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-1',\n        url: '/images/products/organic-moringa-powder.svg',\n        alt: 'Organic Moringa Powder Premium package',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Non-GMO', 'Gluten-Free'],\n    description: 'Premium organic moringa leaf powder sourced from sustainable farms',\n    ingredients: ['100% Organic Moringa Oleifera Leaf Powder'],\n    servingSize: '1 teaspoon (3g)',\n    servingsPerContainer: 100,\n    thirdPartyTested: true,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    amazonASIN: 'B08EXAMPLE1',\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-capsules-500mg',\n    name: 'Moringa Capsules 500mg',\n    brand: 'Nature\\'s Way',\n    type: 'capsules',\n    price: 19.99,\n    rating: 4.6,\n    pros: [\n      'Convenient capsule form',\n      'Standardized 500mg dose',\n      'No taste or mixing required',\n      'Good value for money',\n      'Vegetarian capsules',\n      'Third-party tested'\n    ],\n    cons: [\n      'Lower concentration than powder',\n      'Contains capsule fillers',\n      'More expensive per gram of moringa'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example2',\n        price: 19.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'iherb-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'iHerb',\n        url: 'https://iherb.com/example2',\n        price: 18.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-2',\n        url: '/images/products/moringa-capsules-500mg.svg',\n        alt: 'Moringa Capsules 500mg bottle',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['Non-GMO', 'Vegetarian', 'GMP Certified'],\n    description: 'Convenient moringa leaf capsules with standardized 500mg dose',\n    ingredients: ['Moringa Oleifera Leaf Powder', 'Vegetarian Capsule (Cellulose)'],\n    servingSize: '2 capsules (1000mg)',\n    servingsPerContainer: 60,\n    thirdPartyTested: true,\n    organic: false,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: true,\n    amazonASIN: 'B08EXAMPLE2',\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-leaf-tea-organic',\n    name: 'Organic Moringa Leaf Tea',\n    brand: 'Traditional Medicinals',\n    type: 'tea',\n    price: 16.99,\n    rating: 4.4,\n    pros: [\n      'Organic and fair trade',\n      'Pleasant mild flavor',\n      'Easy to prepare',\n      'Good for daily consumption',\n      'Biodegradable tea bags',\n      'Affordable option'\n    ],\n    cons: [\n      'Lower moringa concentration',\n      'Some may find taste too mild',\n      'Limited shelf life once opened'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example3',\n        price: 16.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'thrive-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Thrive Market',\n        url: 'https://thrivemarket.com/example3',\n        price: 15.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-3',\n        url: '/images/products/moringa-leaf-tea.svg',\n        alt: 'Organic Moringa Leaf Tea box',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Fair Trade', 'Non-GMO'],\n    description: 'Organic moringa leaf tea bags for daily wellness routine',\n    ingredients: ['100% Organic Moringa Oleifera Leaves'],\n    servingSize: '1 tea bag',\n    servingsPerContainer: 20,\n    thirdPartyTested: false,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    createdAt: new Date('2024-02-01'),\n    updatedAt: new Date('2025-01-15')\n  }\n];\n\nexport const getProductBySlug = (slug: string): Product | undefined => {\n  return sampleProducts.find(product => \n    product.id === slug || \n    product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-') === slug\n  );\n};\n\nexport const getFeaturedProducts = (): Product[] => {\n  return sampleProducts.slice(0, 3);\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBA<PERSON>,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAW;SAAc;QAC1D,aAAa;QACb,aAAa;YAAC;SAA4C;QAC1D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAW;YAAc;SAAgB;QAC1D,aAAa;QACb,aAAa;YAAC;YAAgC;SAAiC;QAC/E,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAc;SAAU;QACzD,aAAa;QACb,aAAa;YAAC;SAAuC;QACrD,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,IAAI,CAAC,CAAA,UACzB,QAAQ,EAAE,KAAK,QACf,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,SAAS;AAE/D;AAEO,MAAM,sBAAsB;IACjC,OAAO,eAAe,KAAK,CAAC,GAAG;AACjC", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/reviews/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { sampleProducts } from '@/data/products';\nimport { formatPrice } from '@/lib/utils';\nimport Image from 'next/image';\n\nexport const metadata: Metadata = {\n  title: 'Moringa Supplement Reviews - Plant Based Vitality',\n  description: 'Expert reviews of the best moringa supplements. Independent testing, detailed analysis, and honest recommendations for moringa powder, capsules, and tea.',\n  keywords: ['moringa reviews', 'moringa supplement reviews', 'best moringa products', 'moringa powder reviews'],\n};\n\nexport default function ReviewsPage() {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <StarIcon\n        key={i}\n        className={`h-4 w-4 ${\n          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <header className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-foreground mb-4\">\n            Moringa Supplement Reviews\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n            Expert-tested reviews of the best moringa supplements. Independent analysis, \n            third-party testing, and honest recommendations to help you choose the right product.\n          </p>\n        </header>\n\n        {/* Reviews Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {sampleProducts.map((product) => (\n            <Card key={product.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"aspect-video relative mb-4\">\n                  <Image\n                    src={product.images[0]?.url || '/images/placeholder.svg'}\n                    alt={product.images[0]?.alt || product.name}\n                    fill\n                    className=\"object-cover rounded-lg\"\n                  />\n                </div>\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm text-muted-foreground uppercase\">\n                    {product.type}\n                  </span>\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"flex\">{renderStars(product.rating)}</div>\n                    <span className=\"text-sm font-medium\">{product.rating}</span>\n                  </div>\n                </div>\n                <CardTitle className=\"text-xl\">{product.name}</CardTitle>\n                <CardDescription>\n                  by {product.brand} • {formatPrice(product.price)}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3 mb-4\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Quality</span>\n                    <span className=\"font-medium\">\n                      {product.rating >= 4.5 ? 'Excellent' : \n                       product.rating >= 4.0 ? 'Very Good' : 'Good'}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Type</span>\n                    <span className=\"font-medium capitalize\">{product.type}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Price</span>\n                    <span className=\"font-medium\">{formatPrice(product.price)}</span>\n                  </div>\n                </div>\n\n                {/* Quality Indicators */}\n                <div className=\"flex flex-wrap gap-1 mb-4\">\n                  {product.organic && (\n                    <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                      Organic\n                    </span>\n                  )}\n                  {product.thirdPartyTested && (\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                      Tested\n                    </span>\n                  )}\n                  {product.madeInUSA && (\n                    <span className=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\n                      USA\n                    </span>\n                  )}\n                </div>\n\n                <Link href={`/reviews/${product.id}`}>\n                  <Button className=\"w-full\">Read Full Review</Button>\n                </Link>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center bg-muted/50 rounded-lg p-8\">\n          <h2 className=\"text-2xl font-bold mb-4\">\n            Looking for Our Top Recommendations?\n          </h2>\n          <p className=\"text-muted-foreground mb-6\">\n            Check out our comprehensive ranking of the best moringa supplements of 2025.\n          </p>\n          <Link href=\"/best-moringa-supplements\">\n            <Button size=\"lg\">View Top 5 Rankings</Button>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAmB;QAA8B;QAAyB;KAAyB;AAChH;AAEe,SAAS;IACtB,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,6MAAA,CAAA,WAAQ;gBAEP,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,oBAAoB,iBAC7C;eAHG;;;;;IAMX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAOjE,8OAAC;oBAAI,WAAU;8BACZ,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,wBACnB,8OAAC,gIAAA,CAAA,OAAI;4BAAkB,WAAU;;8CAC/B,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,MAAM,CAAC,EAAE,EAAE,OAAO;gDAC/B,KAAK,QAAQ,MAAM,CAAC,EAAE,EAAE,OAAO,QAAQ,IAAI;gDAC3C,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAQ,YAAY,QAAQ,MAAM;;;;;;sEACjD,8OAAC;4DAAK,WAAU;sEAAuB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;sDAGzD,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,QAAQ,IAAI;;;;;;sDAC5C,8OAAC,gIAAA,CAAA,kBAAe;;gDAAC;gDACX,QAAQ,KAAK;gDAAC;gDAAI,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;;8CAGnD,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;sEACb,QAAQ,MAAM,IAAI,MAAM,cACxB,QAAQ,MAAM,IAAI,MAAM,cAAc;;;;;;;;;;;;8DAG3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;sEAA0B,QAAQ,IAAI;;;;;;;;;;;;8DAExD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;sEAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sDAK5D,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBACd,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAI9E,QAAQ,gBAAgB,kBACvB,8OAAC;oDAAK,WAAU;8DAA2D;;;;;;gDAI5E,QAAQ,SAAS,kBAChB,8OAAC;oDAAK,WAAU;8DAAyD;;;;;;;;;;;;sDAM7E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;sDAClC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;2BA/DtB,QAAQ,EAAE;;;;;;;;;;8BAuEzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}]}