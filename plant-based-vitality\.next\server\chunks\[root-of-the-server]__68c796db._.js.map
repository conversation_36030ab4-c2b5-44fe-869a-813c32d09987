{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, uuid, varchar, decimal, jsonb, text, timestamp, inet, boolean } from 'drizzle-orm/pg-core';\nimport { relations } from 'drizzle-orm';\n\n// Products table - stores all Moringa product information\nexport const products = pgTable('products', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  name: varchar('name', { length: 255 }).notNull(),\n  brand: varchar('brand', { length: 255 }).notNull(),\n  type: varchar('type', { length: 50 }).notNull(), // 'powder', 'capsules', 'tea', 'oil', 'liquid'\n  price: decimal('price', { precision: 10, scale: 2 }),\n  rating: decimal('rating', { precision: 3, scale: 2 }),\n  pros: jsonb('pros'), // Array of strings\n  cons: jsonb('cons'), // Array of strings\n  affiliateLinks: jsonb('affiliate_links'), // Array of affiliate link objects\n  images: jsonb('images'), // Array of image objects\n  certifications: jsonb('certifications'), // Array of certification strings\n  description: text('description'), // Rich text content\n  ingredients: jsonb('ingredients'), // Array of ingredient strings\n  servingSize: varchar('serving_size', { length: 100 }),\n  servingsPerContainer: decimal('servings_per_container', { precision: 10, scale: 0 }),\n  thirdPartyTested: boolean('third_party_tested').default(false),\n  organic: boolean('organic').default(false),\n  nonGmo: boolean('non_gmo').default(false),\n  glutenFree: boolean('gluten_free').default(false),\n  vegan: boolean('vegan').default(false),\n  madeInUSA: boolean('made_in_usa').default(false),\n  amazonASIN: varchar('amazon_asin', { length: 20 }),\n  slug: varchar('slug', { length: 255 }).notNull().unique(),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Affiliate clicks tracking - tracks every click on affiliate links\nexport const affiliateClicks = pgTable('affiliate_clicks', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  productId: uuid('product_id').references(() => products.id),\n  affiliatePartner: varchar('affiliate_partner', { length: 100 }).notNull(), // 'amazon', 'iherb', etc.\n  clickId: varchar('click_id', { length: 255 }).notNull().unique(),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  referrer: text('referrer'),\n  utmSource: varchar('utm_source', { length: 100 }),\n  utmMedium: varchar('utm_medium', { length: 100 }),\n  utmCampaign: varchar('utm_campaign', { length: 100 }),\n  pageUrl: text('page_url'),\n  clickedAt: timestamp('clicked_at').defaultNow(),\n});\n\n// Email subscribers - tracks newsletter signups\nexport const emailSubscribers = pgTable('email_subscribers', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  email: varchar('email', { length: 255 }).notNull().unique(),\n  firstName: varchar('first_name', { length: 100 }),\n  source: varchar('source', { length: 100 }), // 'homepage', 'review-page', etc.\n  convertkitId: varchar('convertkit_id', { length: 100 }),\n  subscribedAt: timestamp('subscribed_at').defaultNow(),\n  unsubscribedAt: timestamp('unsubscribed_at'),\n});\n\n// Analytics events - general event tracking\nexport const analyticsEvents = pgTable('analytics_events', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  eventType: varchar('event_type', { length: 100 }).notNull(), // 'page_view', 'affiliate_click', 'email_signup'\n  pageUrl: text('page_url'),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  eventData: jsonb('event_data'), // Additional event-specific data\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Define relationships\nexport const productsRelations = relations(products, ({ many }) => ({\n  affiliateClicks: many(affiliateClicks),\n}));\n\nexport const affiliateClicksRelations = relations(affiliateClicks, ({ one }) => ({\n  product: one(products, {\n    fields: [affiliateClicks.productId],\n    references: [products.id],\n  }),\n}));\n\n// Type exports for TypeScript\nexport type Product = typeof products.$inferSelect;\nexport type NewProduct = typeof products.$inferInsert;\nexport type AffiliateClick = typeof affiliateClicks.$inferSelect;\nexport type NewAffiliateClick = typeof affiliateClicks.$inferInsert;\nexport type EmailSubscriber = typeof emailSubscribers.$inferSelect;\nexport type NewEmailSubscriber = typeof emailSubscribers.$inferInsert;\nexport type AnalyticsEvent = typeof analyticsEvents.$inferSelect;\nexport type NewAnalyticsEvent = typeof analyticsEvents.$inferInsert;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAG,GAAG,OAAO;IAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAI,OAAO;IAAE;IAClD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,WAAW;QAAG,OAAO;IAAE;IACnD,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,QAAQ,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACd,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,aAAa,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACnB,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B;QAAE,WAAW;QAAI,OAAO;IAAE;IAClF,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,CAAC;IACxD,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACpC,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACnC,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC3C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC1C,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAG;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC1D,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;QAAE,QAAQ;IAAI,GAAG,OAAO;IACvE,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IAC9D,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAI;IACxC,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QAAE,QAAQ;IAAI;IACrD,cAAc,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU;IACnD,gBAAgB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;AAC5B;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI,GAAG,OAAO;IACzD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACjB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAClE,iBAAiB,KAAK;IACxB,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/E,SAAS,IAAI,UAAU;YACrB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,SAAS,EAAE;aAAC;QAC3B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\n// Create a connection pool for better performance\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,\n  max: 20, // Maximum number of clients in the pool\n  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds\n  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established\n});\n\n// Create Drizzle instance with schema\nexport const db = drizzle(pool, { schema });\n\n// Export schema for use in other files\nexport * from './schema';\n\n// Helper function to test database connection\nexport async function testConnection() {\n  try {\n    const client = await pool.connect();\n    const result = await client.query('SELECT NOW()');\n    client.release();\n    console.log('✅ Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('❌ Database connection failed:', error);\n    return false;\n  }\n}\n\n// Graceful shutdown\nprocess.on('SIGINT', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;;AAEA,kDAAkD;AAClD,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK,sCAAwC,0BAAgC;IAC7E,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO;;AAMlC,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;QAClC,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,sCAAsC,OAAO,IAAI,CAAC,EAAE;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,UAAU;IACnB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf;AAEA,QAAQ,EAAE,CAAC,WAAW;IACpB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/api/email/subscribe/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { db, emailSubscribers, analyticsEvents } from '@/lib/db';\nimport { eq } from 'drizzle-orm';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, firstName, source } = body;\n\n    // Validate email\n    if (!email || !isValidEmail(email)) {\n      return NextResponse.json(\n        { error: 'Valid email address is required' },\n        { status: 400 }\n      );\n    }\n\n    // Check if email already exists\n    const existingSubscriber = await db\n      .select()\n      .from(emailSubscribers)\n      .where(eq(emailSubscribers.email, email.toLowerCase()))\n      .limit(1);\n\n    if (existingSubscriber.length > 0) {\n      // Check if previously unsubscribed\n      if (existingSubscriber[0].unsubscribedAt) {\n        // Resubscribe\n        await db\n          .update(emailSubscribers)\n          .set({\n            unsubscribedAt: null,\n            subscribedAt: new Date(),\n            source: source || 'website',\n          })\n          .where(eq(emailSubscribers.email, email.toLowerCase()));\n\n        console.log(`✅ Email resubscribed: ${email}`);\n      } else {\n        return NextResponse.json(\n          { error: 'Email already subscribed' },\n          { status: 409 }\n        );\n      }\n    } else {\n      // New subscriber\n      let convertkitId = null;\n\n      // Try to subscribe to ConvertKit if API key is available\n      if (process.env.CONVERTKIT_API_KEY && process.env.CONVERTKIT_FORM_ID) {\n        try {\n          convertkitId = await subscribeToConvertKit(email, firstName);\n        } catch (error) {\n          console.warn('⚠️ ConvertKit subscription failed:', error);\n          // Continue without ConvertKit - we'll still save to our database\n        }\n      }\n\n      // Save to our database\n      await db.insert(emailSubscribers).values({\n        email: email.toLowerCase(),\n        firstName: firstName || null,\n        source: source || 'website',\n        convertkitId,\n      });\n\n      console.log(`✅ New email subscriber: ${email}`);\n    }\n\n    // Track analytics event\n    const userAgent = request.headers.get('user-agent') || '';\n    const forwardedFor = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';\n\n    await db.insert(analyticsEvents).values({\n      eventType: 'email_signup',\n      pageUrl: request.headers.get('referer') || '',\n      userIp,\n      userAgent,\n      eventData: {\n        email: email.toLowerCase(),\n        source: source || 'website',\n        hasConvertKit: !!process.env.CONVERTKIT_API_KEY,\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      message: 'Successfully subscribed to newsletter!',\n    });\n\n  } catch (error) {\n    console.error('❌ Email subscription failed:', error);\n    return NextResponse.json(\n      { error: 'Failed to subscribe. Please try again.' },\n      { status: 500 }\n    );\n  }\n}\n\n// Helper function to validate email\nfunction isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Helper function to subscribe to ConvertKit\nasync function subscribeToConvertKit(email: string, firstName?: string): Promise<string | null> {\n  const apiKey = process.env.CONVERTKIT_API_KEY;\n  const formId = process.env.CONVERTKIT_FORM_ID;\n\n  if (!apiKey || !formId) {\n    throw new Error('ConvertKit API credentials not configured');\n  }\n\n  const response = await fetch(`https://api.convertkit.com/v3/forms/${formId}/subscribe`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      api_key: apiKey,\n      email,\n      first_name: firstName || '',\n    }),\n  });\n\n  if (!response.ok) {\n    const errorData = await response.text();\n    throw new Error(`ConvertKit API error: ${response.status} - ${errorData}`);\n  }\n\n  const data = await response.json();\n  return data.subscription?.subscriber?.id?.toString() || null;\n}\n\n// GET endpoint to check subscription status\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const email = searchParams.get('email');\n\n    if (!email || !isValidEmail(email)) {\n      return NextResponse.json(\n        { error: 'Valid email address is required' },\n        { status: 400 }\n      );\n    }\n\n    const subscriber = await db\n      .select({\n        email: emailSubscribers.email,\n        subscribedAt: emailSubscribers.subscribedAt,\n        unsubscribedAt: emailSubscribers.unsubscribedAt,\n        source: emailSubscribers.source,\n      })\n      .from(emailSubscribers)\n      .where(eq(emailSubscribers.email, email.toLowerCase()))\n      .limit(1);\n\n    if (subscriber.length === 0) {\n      return NextResponse.json({\n        subscribed: false,\n        message: 'Email not found',\n      });\n    }\n\n    const isSubscribed = !subscriber[0].unsubscribedAt;\n\n    return NextResponse.json({\n      subscribed: isSubscribed,\n      subscribedAt: subscriber[0].subscribedAt,\n      source: subscriber[0].source,\n    });\n\n  } catch (error) {\n    console.error('❌ Subscription check failed:', error);\n    return NextResponse.json(\n      { error: 'Failed to check subscription status' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG;QAErC,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,aAAa,QAAQ;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,qBAAqB,MAAM,2IAAA,CAAA,KAAE,CAChC,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,mBAAgB,EACrB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,KAAK,EAAE,MAAM,WAAW,KAClD,KAAK,CAAC;QAET,IAAI,mBAAmB,MAAM,GAAG,GAAG;YACjC,mCAAmC;YACnC,IAAI,kBAAkB,CAAC,EAAE,CAAC,cAAc,EAAE;gBACxC,cAAc;gBACd,MAAM,2IAAA,CAAA,KAAE,CACL,MAAM,CAAC,4HAAA,CAAA,mBAAgB,EACvB,GAAG,CAAC;oBACH,gBAAgB;oBAChB,cAAc,IAAI;oBAClB,QAAQ,UAAU;gBACpB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,KAAK,EAAE,MAAM,WAAW;gBAErD,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,OAAO;YAC9C,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA2B,GACpC;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,iBAAiB;YACjB,IAAI,eAAe;YAEnB,yDAAyD;YACzD,IAAI,QAAQ,GAAG,CAAC,kBAAkB,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;gBACpE,IAAI;oBACF,eAAe,MAAM,sBAAsB,OAAO;gBACpD,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,sCAAsC;gBACnD,iEAAiE;gBACnE;YACF;YAEA,uBAAuB;YACvB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC;gBACvC,OAAO,MAAM,WAAW;gBACxB,WAAW,aAAa;gBACxB,QAAQ,UAAU;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO;QAChD;QAEA,wBAAwB;QACxB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACvD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,SAAS,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU,QAAQ,EAAE,IAAI;QAEtE,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,kBAAe,EAAE,MAAM,CAAC;YACtC,WAAW;YACX,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;YAC3C;YACA;YACA,WAAW;gBACT,OAAO,MAAM,WAAW;gBACxB,QAAQ,UAAU;gBAClB,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,kBAAkB;YACjD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyC,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,oCAAoC;AACpC,SAAS,aAAa,KAAa;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,6CAA6C;AAC7C,eAAe,sBAAsB,KAAa,EAAE,SAAkB;IACpE,MAAM,SAAS,QAAQ,GAAG,CAAC,kBAAkB;IAC7C,MAAM,SAAS,QAAQ,GAAG,CAAC,kBAAkB;IAE7C,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,CAAC,oCAAoC,EAAE,OAAO,UAAU,CAAC,EAAE;QACtF,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,SAAS;YACT;YACA,YAAY,aAAa;QAC3B;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;IAC3E;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,YAAY,EAAE,YAAY,IAAI,cAAc;AAC1D;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAE/B,IAAI,CAAC,SAAS,CAAC,aAAa,QAAQ;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,MAAM,2IAAA,CAAA,KAAE,CACxB,MAAM,CAAC;YACN,OAAO,4HAAA,CAAA,mBAAgB,CAAC,KAAK;YAC7B,cAAc,4HAAA,CAAA,mBAAgB,CAAC,YAAY;YAC3C,gBAAgB,4HAAA,CAAA,mBAAgB,CAAC,cAAc;YAC/C,QAAQ,4HAAA,CAAA,mBAAgB,CAAC,MAAM;QACjC,GACC,IAAI,CAAC,4HAAA,CAAA,mBAAgB,EACrB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,KAAK,EAAE,MAAM,WAAW,KAClD,KAAK,CAAC;QAET,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,YAAY;gBACZ,SAAS;YACX;QACF;QAEA,MAAM,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc;QAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,YAAY;YACZ,cAAc,UAAU,CAAC,EAAE,CAAC,YAAY;YACxC,QAAQ,UAAU,CAAC,EAAE,CAAC,MAAM;QAC9B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsC,GAC/C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}