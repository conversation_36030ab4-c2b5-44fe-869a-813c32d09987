import { NextRequest, NextResponse } from 'next/server';
import { db, emailSubscribers, analyticsEvents } from '@/lib/db';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, firstName, source } = body;

    // Validate email
    if (!email || !isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Valid email address is required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingSubscriber = await db
      .select()
      .from(emailSubscribers)
      .where(eq(emailSubscribers.email, email.toLowerCase()))
      .limit(1);

    if (existingSubscriber.length > 0) {
      // Check if previously unsubscribed
      if (existingSubscriber[0].unsubscribedAt) {
        // Resubscribe
        await db
          .update(emailSubscribers)
          .set({
            unsubscribedAt: null,
            subscribedAt: new Date(),
            source: source || 'website',
          })
          .where(eq(emailSubscribers.email, email.toLowerCase()));

        console.log(`✅ Email resubscribed: ${email}`);
      } else {
        return NextResponse.json(
          { error: 'Email already subscribed' },
          { status: 409 }
        );
      }
    } else {
      // New subscriber
      let convertkitId = null;

      // Try to subscribe to ConvertKit if API key is available
      if (process.env.CONVERTKIT_API_KEY && process.env.CONVERTKIT_FORM_ID) {
        try {
          convertkitId = await subscribeToConvertKit(email, firstName);
        } catch (error) {
          console.warn('⚠️ ConvertKit subscription failed:', error);
          // Continue without ConvertKit - we'll still save to our database
        }
      }

      // Save to our database
      await db.insert(emailSubscribers).values({
        email: email.toLowerCase(),
        firstName: firstName || null,
        source: source || 'website',
        convertkitId,
      });

      console.log(`✅ New email subscriber: ${email}`);
    }

    // Track analytics event
    const userAgent = request.headers.get('user-agent') || '';
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';

    await db.insert(analyticsEvents).values({
      eventType: 'email_signup',
      pageUrl: request.headers.get('referer') || '',
      userIp,
      userAgent,
      eventData: {
        email: email.toLowerCase(),
        source: source || 'website',
        hasConvertKit: !!process.env.CONVERTKIT_API_KEY,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter!',
    });

  } catch (error) {
    console.error('❌ Email subscription failed:', error);
    return NextResponse.json(
      { error: 'Failed to subscribe. Please try again.' },
      { status: 500 }
    );
  }
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to subscribe to ConvertKit
async function subscribeToConvertKit(email: string, firstName?: string): Promise<string | null> {
  const apiKey = process.env.CONVERTKIT_API_KEY;
  const formId = process.env.CONVERTKIT_FORM_ID;

  if (!apiKey || !formId) {
    throw new Error('ConvertKit API credentials not configured');
  }

  const response = await fetch(`https://api.convertkit.com/v3/forms/${formId}/subscribe`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      api_key: apiKey,
      email,
      first_name: firstName || '',
    }),
  });

  if (!response.ok) {
    const errorData = await response.text();
    throw new Error(`ConvertKit API error: ${response.status} - ${errorData}`);
  }

  const data = await response.json();
  return data.subscription?.subscriber?.id?.toString() || null;
}

// GET endpoint to check subscription status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email || !isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Valid email address is required' },
        { status: 400 }
      );
    }

    const subscriber = await db
      .select({
        email: emailSubscribers.email,
        subscribedAt: emailSubscribers.subscribedAt,
        unsubscribedAt: emailSubscribers.unsubscribedAt,
        source: emailSubscribers.source,
      })
      .from(emailSubscribers)
      .where(eq(emailSubscribers.email, email.toLowerCase()))
      .limit(1);

    if (subscriber.length === 0) {
      return NextResponse.json({
        subscribed: false,
        message: 'Email not found',
      });
    }

    const isSubscribed = !subscriber[0].unsubscribedAt;

    return NextResponse.json({
      subscribed: isSubscribed,
      subscribedAt: subscriber[0].subscribedAt,
      source: subscriber[0].source,
    });

  } catch (error) {
    console.error('❌ Subscription check failed:', error);
    return NextResponse.json(
      { error: 'Failed to check subscription status' },
      { status: 500 }
    );
  }
}
