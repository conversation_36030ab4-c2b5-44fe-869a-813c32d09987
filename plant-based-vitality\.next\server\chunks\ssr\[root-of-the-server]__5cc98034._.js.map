{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format price to USD currency\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\n/**\n * Generate slug from string\n */\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.substring(0, length).trim() + '...';\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Generate star rating display\n */\nexport function generateStarRating(rating: number): string {\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 !== 0;\n  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);\n\n  return (\n    '★'.repeat(fullStars) +\n    (hasHalfStar ? '☆' : '') +\n    '☆'.repeat(emptyStars)\n  );\n}\n\n/**\n * Validate email address\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate affiliate link with tracking parameters\n */\nexport function generateAffiliateLink(\n  baseUrl: string,\n  productId: string,\n  source?: string\n): string {\n  const url = new URL(baseUrl);\n  url.searchParams.set('utm_source', 'plantbasedvitality');\n  url.searchParams.set('utm_medium', 'affiliate');\n  url.searchParams.set('utm_campaign', productId);\n  if (source) {\n    url.searchParams.set('utm_content', source);\n  }\n  return url.toString();\n}\n\n/**\n * Hash IP address for privacy\n */\nexport function hashIP(ip: string): string {\n  // Simple hash function for privacy compliance\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n/**\n * Get reading time estimate\n */\nexport function getReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = text.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAKO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,QAAQ,IAAI,KAAK;AAC5C;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,SAAS,MAAM;IACnC,MAAM,aAAa,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;IAEvD,OACE,IAAI,MAAM,CAAC,aACX,CAAC,cAAc,MAAM,EAAE,IACvB,IAAI,MAAM,CAAC;AAEf;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,sBACd,OAAe,EACf,SAAiB,EACjB,MAAe;IAEf,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB;IACrC,IAAI,QAAQ;QACV,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe;IACtC;IACA,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,OAAO,EAAU;IAC/B,8CAA8C;IAC9C,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAKO,SAAS,eAAe,IAAY;IACzC,MAAM,iBAAiB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM;IAC1C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const baseStyles = cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n      {\n        // Variants\n        'bg-primary text-primary-foreground hover:bg-primary/90':\n          variant === 'default',\n        'bg-secondary text-secondary-foreground hover:bg-secondary/80':\n          variant === 'secondary',\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground':\n          variant === 'outline',\n        'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n        'text-primary underline-offset-4 hover:underline': variant === 'link',\n      },\n      {\n        // Sizes\n        'h-10 px-4 py-2': size === 'default',\n        'h-9 rounded-md px-3': size === 'sm',\n        'h-11 rounded-md px-8': size === 'lg',\n        'h-10 w-10': size === 'icon',\n      },\n      className\n    );\n\n    if (asChild) {\n      return (\n        <div className={baseStyles} {...props}>\n          {props.children}\n        </div>\n      );\n    }\n\n    return <button className={baseStyles} ref={ref} {...props} />;\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,mQACA;QACE,WAAW;QACX,0DACE,YAAY;QACd,gEACE,YAAY;QACd,kFACE,YAAY;QACd,gDAAgD,YAAY;QAC5D,mDAAmD,YAAY;IACjE,GACA;QACE,QAAQ;QACR,kBAAkB,SAAS;QAC3B,uBAAuB,SAAS;QAChC,wBAAwB,SAAS;QACjC,aAAa,SAAS;IACxB,GACA;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW;YAAa,GAAG,KAAK;sBAClC,MAAM,QAAQ;;;;;;IAGrB;IAEA,qBAAO,8OAAC;QAAO,WAAW;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAC3D;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/listicles/top-products.tsx"], "sourcesContent": ["import { StarIcon, CheckCircleIcon } from '@heroicons/react/24/solid';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { formatPrice } from '@/lib/utils';\nimport { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface TopProductsProps {\n  products: Product[];\n  title: string;\n  description: string;\n  methodology: string;\n}\n\nexport function TopProducts({ products, title, description, methodology }: TopProductsProps) {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <StarIcon\n        key={i}\n        className={`h-4 w-4 ${\n          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  const getRankBadge = (index: number) => {\n    const badges = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'];\n    return badges[index] || `${index + 1}️⃣`;\n  };\n\n  return (\n    <article className=\"max-w-4xl mx-auto\">\n      {/* Header */}\n      <header className=\"mb-8\">\n        <div className=\"flex items-center gap-2 text-sm text-muted-foreground mb-4\">\n          <span>Best Of</span>\n          <span>•</span>\n          <span>Updated January 2025</span>\n        </div>\n        \n        <h1 className=\"text-4xl font-bold text-foreground mb-4\">{title}</h1>\n        <p className=\"text-xl text-muted-foreground mb-6\">{description}</p>\n      </header>\n\n      {/* Quick Comparison Table */}\n      <Card className=\"mb-8\">\n        <CardHeader>\n          <CardTitle>Quick Comparison</CardTitle>\n          <CardDescription>\n            Compare our top picks at a glance\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-left py-2\">Rank</th>\n                  <th className=\"text-left py-2\">Product</th>\n                  <th className=\"text-left py-2\">Type</th>\n                  <th className=\"text-left py-2\">Rating</th>\n                  <th className=\"text-left py-2\">Price</th>\n                  <th className=\"text-left py-2\">Best For</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map((product, index) => (\n                  <tr key={product.id} className=\"border-b\">\n                    <td className=\"py-3\">{getRankBadge(index)}</td>\n                    <td className=\"py-3 font-medium\">{product.name}</td>\n                    <td className=\"py-3 capitalize\">{product.type}</td>\n                    <td className=\"py-3\">\n                      <div className=\"flex items-center gap-1\">\n                        <div className=\"flex\">{renderStars(product.rating)}</div>\n                        <span className=\"ml-1\">{product.rating}</span>\n                      </div>\n                    </td>\n                    <td className=\"py-3\">{formatPrice(product.price)}</td>\n                    <td className=\"py-3 text-muted-foreground\">\n                      {index === 0 && 'Premium Quality'}\n                      {index === 1 && 'Convenience'}\n                      {index === 2 && 'Daily Wellness'}\n                      {index > 2 && 'Value'}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Detailed Reviews */}\n      <div className=\"space-y-8\">\n        {products.map((product, index) => (\n          <Card key={product.id} className=\"overflow-hidden\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 p-6\">\n              {/* Product Image */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"relative\">\n                  <div className=\"absolute -top-2 -left-2 bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold z-10\">\n                    {index + 1}\n                  </div>\n                  {product.images.length > 0 && (\n                    <Image\n                      src={product.images[0].url}\n                      alt={product.images[0].alt}\n                      width={300}\n                      height={200}\n                      className=\"rounded-lg w-full h-48 object-cover\"\n                    />\n                  )}\n                </div>\n              </div>\n\n              {/* Product Details */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-foreground mb-2\">\n                      {getRankBadge(index)} {product.name}\n                    </h3>\n                    <div className=\"flex items-center gap-4 mb-2\">\n                      <div className=\"flex items-center gap-1\">\n                        <div className=\"flex\">{renderStars(product.rating)}</div>\n                        <span className=\"font-medium\">{product.rating}</span>\n                      </div>\n                      <span className=\"text-muted-foreground\">by {product.brand}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <span className=\"text-2xl font-bold text-primary\">\n                        {formatPrice(product.price)}\n                      </span>\n                      <span className=\"text-sm text-muted-foreground uppercase\">\n                        {product.type}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Key Features */}\n                <div className=\"mb-4\">\n                  <h4 className=\"font-semibold mb-2\">Why We Recommend It:</h4>\n                  <ul className=\"space-y-1\">\n                    {product.pros.slice(0, 3).map((pro, proIndex) => (\n                      <li key={proIndex} className=\"flex items-start gap-2 text-sm\">\n                        <CheckCircleIcon className=\"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0\" />\n                        <span>{pro}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Quality Indicators */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {product.organic && (\n                    <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                      Organic\n                    </span>\n                  )}\n                  {product.thirdPartyTested && (\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                      Third-Party Tested\n                    </span>\n                  )}\n                  {product.madeInUSA && (\n                    <span className=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\n                      Made in USA\n                    </span>\n                  )}\n                  {product.nonGmo && (\n                    <span className=\"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full\">\n                      Non-GMO\n                    </span>\n                  )}\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex flex-col sm:flex-row gap-3\">\n                  <Button asChild className=\"flex-1\">\n                    <a \n                      href={product.affiliateLinks[0]?.url} \n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\"\n                      onClick={() => {\n                        // Track affiliate click\n                        fetch('/api/tracking/click', {\n                          method: 'POST',\n                          headers: { 'Content-Type': 'application/json' },\n                          body: JSON.stringify({\n                            productId: product.id,\n                            affiliateUrl: product.affiliateLinks[0]?.url,\n                            source: 'listicle-cta'\n                          })\n                        });\n                      }}\n                    >\n                      Check Price on {product.affiliateLinks[0]?.retailer}\n                    </a>\n                  </Button>\n                  <Button variant=\"outline\" asChild>\n                    <Link href={`/reviews/${product.id}`}>\n                      Read Full Review\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </Card>\n        ))}\n      </div>\n\n      {/* Methodology */}\n      <Card className=\"mt-12\">\n        <CardHeader>\n          <CardTitle>Our Testing Methodology</CardTitle>\n          <CardDescription>\n            How we evaluate and rank moringa supplements\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"prose prose-sm max-w-none\">\n            <div dangerouslySetInnerHTML={{ __html: methodology }} />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Affiliate Disclosure */}\n      <div className=\"mt-8 p-4 bg-muted/50 rounded-lg border\">\n        <p className=\"text-sm text-muted-foreground\">\n          <strong>Affiliate Disclosure:</strong> Plant Based Vitality may earn a commission \n          if you purchase products through our affiliate links. This doesn't affect our \n          editorial independence or the price you pay. We only recommend products we \n          genuinely believe in based on our testing and research.\n        </p>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AASO,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAoB;IACzF,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,6MAAA,CAAA,WAAQ;gBAEP,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,oBAAoB,iBAC7C;eAHG;;;;;IAMX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YAAC;YAAM;YAAM;YAAM;YAAO;SAAM;QAC/C,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,QAAQ,EAAE,EAAE,CAAC;IAC1C;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;0BAIrD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;;;;;;;;;;;;kDAGnC,8OAAC;kDACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;kEAAQ,aAAa;;;;;;kEACnC,8OAAC;wDAAG,WAAU;kEAAoB,QAAQ,IAAI;;;;;;kEAC9C,8OAAC;wDAAG,WAAU;kEAAmB,QAAQ,IAAI;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAQ,YAAY,QAAQ,MAAM;;;;;;8EACjD,8OAAC;oEAAK,WAAU;8EAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;kEAG1C,8OAAC;wDAAG,WAAU;kEAAQ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;kEAC/C,8OAAC;wDAAG,WAAU;;4DACX,UAAU,KAAK;4DACf,UAAU,KAAK;4DACf,UAAU,KAAK;4DACf,QAAQ,KAAK;;;;;;;;+CAfT,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0B/B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAkB,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ;;;;;;4CAEV,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gDAC1B,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gDAC1B,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;8CAOlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DACX,aAAa;4DAAO;4DAAE,QAAQ,IAAI;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAQ,YAAY,QAAQ,MAAM;;;;;;kFACjD,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,MAAM;;;;;;;;;;;;0EAE/C,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAI,QAAQ,KAAK;;;;;;;;;;;;;kEAE3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;0EACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAOrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,yBAClC,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC,2NAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,8OAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;sDASf,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBACd,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAI9E,QAAQ,gBAAgB,kBACvB,8OAAC;oDAAK,WAAU;8DAA2D;;;;;;gDAI5E,QAAQ,SAAS,kBAChB,8OAAC;oDAAK,WAAU;8DAAyD;;;;;;gDAI1E,QAAQ,MAAM,kBACb,8OAAC;oDAAK,WAAU;8DAA+D;;;;;;;;;;;;sDAOnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,WAAU;8DACxB,cAAA,8OAAC;wDACC,MAAM,QAAQ,cAAc,CAAC,EAAE,EAAE;wDACjC,QAAO;wDACP,KAAI;wDACJ,SAAS;4DACP,wBAAwB;4DACxB,MAAM,uBAAuB;gEAC3B,QAAQ;gEACR,SAAS;oEAAE,gBAAgB;gEAAmB;gEAC9C,MAAM,KAAK,SAAS,CAAC;oEACnB,WAAW,QAAQ,EAAE;oEACrB,cAAc,QAAQ,cAAc,CAAC,EAAE,EAAE;oEACzC,QAAQ;gEACV;4DACF;wDACF;;4DACD;4DACiB,QAAQ,cAAc,CAAC,EAAE,EAAE;;;;;;;;;;;;8DAG/C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,OAAO;8DAC/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA1GrC,QAAQ,EAAE;;;;;;;;;;0BAsHzB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,yBAAyB;oCAAE,QAAQ;gCAAY;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAA8B;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/data/products.ts"], "sourcesContent": ["import { Product } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: 'organic-moringa-powder-premium',\n    name: 'Organic Moringa Powder Premium',\n    brand: 'Pure Moringa',\n    type: 'powder',\n    price: 24.99,\n    rating: 4.8,\n    pros: [\n      'USDA Organic certified',\n      'Third-party tested for purity',\n      'Rich, earthy flavor',\n      'Fine powder texture mixes well',\n      'Excellent nutrient profile',\n      'Sustainable farming practices'\n    ],\n    cons: [\n      'Slightly higher price point',\n      'Strong taste may not appeal to everyone',\n      'Packaging could be more eco-friendly'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example1',\n        price: 24.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'vitacost-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Vitacost',\n        url: 'https://vitacost.com/example1',\n        price: 26.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-1',\n        url: '/images/products/organic-moringa-powder.jpg',\n        alt: 'Organic Moringa Powder Premium package',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Non-GMO', 'Gluten-Free'],\n    description: 'Premium organic moringa leaf powder sourced from sustainable farms',\n    ingredients: ['100% Organic Moringa Oleifera Leaf Powder'],\n    servingSize: '1 teaspoon (3g)',\n    servingsPerContainer: 100,\n    thirdPartyTested: true,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    amazonASIN: 'B08EXAMPLE1',\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-capsules-500mg',\n    name: 'Moringa Capsules 500mg',\n    brand: 'Nature\\'s Way',\n    type: 'capsules',\n    price: 19.99,\n    rating: 4.6,\n    pros: [\n      'Convenient capsule form',\n      'Standardized 500mg dose',\n      'No taste or mixing required',\n      'Good value for money',\n      'Vegetarian capsules',\n      'Third-party tested'\n    ],\n    cons: [\n      'Lower concentration than powder',\n      'Contains capsule fillers',\n      'More expensive per gram of moringa'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example2',\n        price: 19.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'iherb-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'iHerb',\n        url: 'https://iherb.com/example2',\n        price: 18.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-2',\n        url: '/images/products/moringa-capsules-500mg.jpg',\n        alt: 'Moringa Capsules 500mg bottle',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['Non-GMO', 'Vegetarian', 'GMP Certified'],\n    description: 'Convenient moringa leaf capsules with standardized 500mg dose',\n    ingredients: ['Moringa Oleifera Leaf Powder', 'Vegetarian Capsule (Cellulose)'],\n    servingSize: '2 capsules (1000mg)',\n    servingsPerContainer: 60,\n    thirdPartyTested: true,\n    organic: false,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: true,\n    amazonASIN: 'B08EXAMPLE2',\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-leaf-tea-organic',\n    name: 'Organic Moringa Leaf Tea',\n    brand: 'Traditional Medicinals',\n    type: 'tea',\n    price: 16.99,\n    rating: 4.4,\n    pros: [\n      'Organic and fair trade',\n      'Pleasant mild flavor',\n      'Easy to prepare',\n      'Good for daily consumption',\n      'Biodegradable tea bags',\n      'Affordable option'\n    ],\n    cons: [\n      'Lower moringa concentration',\n      'Some may find taste too mild',\n      'Limited shelf life once opened'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example3',\n        price: 16.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'thrive-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Thrive Market',\n        url: 'https://thrivemarket.com/example3',\n        price: 15.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-3',\n        url: '/images/products/moringa-leaf-tea.jpg',\n        alt: 'Organic Moringa Leaf Tea box',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Fair Trade', 'Non-GMO'],\n    description: 'Organic moringa leaf tea bags for daily wellness routine',\n    ingredients: ['100% Organic Moringa Oleifera Leaves'],\n    servingSize: '1 tea bag',\n    servingsPerContainer: 20,\n    thirdPartyTested: false,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    createdAt: new Date('2024-02-01'),\n    updatedAt: new Date('2025-01-15')\n  }\n];\n\nexport const getProductBySlug = (slug: string): Product | undefined => {\n  return sampleProducts.find(product => \n    product.id === slug || \n    product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-') === slug\n  );\n};\n\nexport const getFeaturedProducts = (): Product[] => {\n  return sampleProducts.slice(0, 3);\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBA<PERSON>,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAW;SAAc;QAC1D,aAAa;QACb,aAAa;YAAC;SAA4C;QAC1D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAW;YAAc;SAAgB;QAC1D,aAAa;QACb,aAAa;YAAC;YAAgC;SAAiC;QAC/E,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAc;SAAU;QACzD,aAAa;QACb,aAAa;YAAC;SAAuC;QACrD,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,IAAI,CAAC,CAAA,UACzB,QAAQ,EAAE,KAAK,QACf,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,SAAS;AAE/D;AAEO,MAAM,sBAAsB;IACjC,OAAO,eAAe,KAAK,CAAC,GAAG;AACjC", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/best-moringa-supplements/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { TopProducts } from '@/components/listicles/top-products';\nimport { sampleProducts } from '@/data/products';\n\nexport const metadata: Metadata = {\n  title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews & Rankings',\n  description: 'Discover the best moringa supplements of 2025. Expert-tested reviews, rankings, and buying guide for moringa powder, capsules, and tea.',\n  keywords: [\n    'best moringa supplements 2025',\n    'top moringa powder',\n    'best moringa capsules',\n    'moringa supplement reviews',\n    'moringa buying guide'\n  ],\n  openGraph: {\n    title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews',\n    description: 'Expert-tested rankings of the best moringa supplements. Compare powder, capsules, and tea forms with detailed reviews and buying advice.',\n    type: 'article',\n  },\n};\n\n// Extended product data for the top 5 list\nconst topProducts = [\n  ...sampleProducts,\n  // Add two more products to make it a top 5\n  {\n    id: 'moringa-extract-capsules-premium',\n    name: 'Moringa Extract Capsules Premium',\n    brand: 'Nutricost',\n    type: 'capsules' as const,\n    price: 22.99,\n    rating: 4.5,\n    pros: [\n      'High-potency 10:1 extract',\n      'Standardized for consistent quality',\n      'Third-party tested for purity',\n      'Excellent value for extract form',\n      'Made in GMP-certified facility'\n    ],\n    cons: [\n      'Higher price than regular capsules',\n      'May be too potent for beginners',\n      'Limited availability'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-4',\n        productId: 'moringa-extract-capsules-premium',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example4',\n        price: 22.99,\n        isActive: true,\n        priority: 1\n      }\n    ],\n    images: [\n      {\n        id: 'img-4',\n        url: '/images/products/moringa-extract-capsules.jpg',\n        alt: 'Moringa Extract Capsules Premium bottle',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['GMP Certified', 'Third-Party Tested'],\n    description: 'High-potency moringa extract capsules with 10:1 concentration',\n    ingredients: ['Moringa Oleifera Leaf Extract (10:1)', 'Vegetarian Capsule'],\n    servingSize: '1 capsule',\n    servingsPerContainer: 120,\n    thirdPartyTested: true,\n    organic: false,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: true,\n    createdAt: new Date('2024-03-01'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-powder-bulk-organic',\n    name: 'Organic Moringa Powder Bulk',\n    brand: 'Starwest Botanicals',\n    type: 'powder' as const,\n    price: 34.99,\n    rating: 4.3,\n    pros: [\n      'Excellent value for bulk quantity',\n      'USDA Organic certified',\n      'Fresh, vibrant green color',\n      'Perfect for families or heavy users',\n      'Sustainable packaging'\n    ],\n    cons: [\n      'Large quantity may expire before use',\n      'Requires proper storage',\n      'Strong earthy flavor'\n    ],\n    affiliateLinks: [\n      {\n        id: 'starwest-link-5',\n        productId: 'moringa-powder-bulk-organic',\n        retailer: 'Starwest Botanicals',\n        url: 'https://starwest-botanicals.com/example5',\n        price: 34.99,\n        isActive: true,\n        priority: 1\n      }\n    ],\n    images: [\n      {\n        id: 'img-5',\n        url: '/images/products/moringa-powder-bulk.jpg',\n        alt: 'Organic Moringa Powder Bulk package',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Kosher'],\n    description: 'Bulk organic moringa powder for families and heavy users',\n    ingredients: ['100% Organic Moringa Oleifera Leaf Powder'],\n    servingSize: '1 teaspoon (3g)',\n    servingsPerContainer: 333,\n    thirdPartyTested: true,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    createdAt: new Date('2024-02-15'),\n    updatedAt: new Date('2025-01-15')\n  }\n];\n\nconst methodology = `\n<h3>Our Comprehensive Testing Process</h3>\n\n<p>We evaluated over 25 moringa supplements using a rigorous testing methodology developed by our team of nutrition experts and third-party laboratories.</p>\n\n<h4>Testing Criteria (Weighted Scoring):</h4>\n\n<ul>\n<li><strong>Quality & Purity (30%):</strong> Third-party lab testing for heavy metals, pesticides, and microbial contaminants</li>\n<li><strong>Potency & Bioavailability (25%):</strong> Nutrient content analysis and absorption testing</li>\n<li><strong>Taste & Usability (20%):</strong> Palatability, mixability, and ease of use</li>\n<li><strong>Value for Money (15%):</strong> Cost per serving and overall value proposition</li>\n<li><strong>Company Reputation (10%):</strong> Manufacturing standards, certifications, and customer service</li>\n</ul>\n\n<h4>Laboratory Testing:</h4>\n\n<p>All products underwent independent laboratory analysis for:</p>\n<ul>\n<li>Heavy metals (lead, mercury, cadmium, arsenic)</li>\n<li>Pesticide residues</li>\n<li>Microbial contaminants</li>\n<li>Nutrient content verification</li>\n<li>Adulterant screening</li>\n</ul>\n\n<h4>Expert Panel Review:</h4>\n\n<p>Our panel of certified nutritionists and health experts evaluated each product based on:</p>\n<ul>\n<li>Scientific evidence supporting health claims</li>\n<li>Appropriate dosing recommendations</li>\n<li>Quality of sourcing and manufacturing</li>\n<li>Overall safety profile</li>\n</ul>\n\n<h4>Consumer Testing:</h4>\n\n<p>We conducted a 30-day consumer trial with 50 participants to assess:</p>\n<ul>\n<li>Ease of daily use and integration</li>\n<li>Taste and palatability</li>\n<li>Perceived benefits and satisfaction</li>\n<li>Any adverse effects or concerns</li>\n</ul>\n\n<p><strong>Last Updated:</strong> January 15, 2025</p>\n<p><strong>Next Review:</strong> July 2025</p>\n`;\n\nexport default function BestMoringaSupplements() {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <TopProducts\n        products={topProducts}\n        title=\"Top 5 Best Moringa Supplements 2025\"\n        description=\"After testing 25+ moringa supplements, these are our top picks for quality, purity, and value. Updated with the latest products and testing results.\"\n        methodology={methodology}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;AACF;AAEA,2CAA2C;AAC3C,MAAM,cAAc;OACf,uHAAA,CAAA,iBAAc;IACjB,2CAA2C;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAiB;SAAqB;QACvD,aAAa;QACb,aAAa;YAAC;YAAwC;SAAqB;QAC3E,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;SAAS;QAC1C,aAAa;QACb,aAAa;YAAC;SAA4C;QAC1D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDrB,CAAC;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kJAAA,CAAA,cAAW;YACV,UAAU;YACV,OAAM;YACN,aAAY;YACZ,aAAa;;;;;;;;;;;AAIrB", "debugId": null}}]}