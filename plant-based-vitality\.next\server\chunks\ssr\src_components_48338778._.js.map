{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/listicles/top-products.tsx"], "sourcesContent": ["'use client';\n\nimport { StarIcon, CheckCircleIcon } from '@heroicons/react/24/solid';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { formatPrice } from '@/lib/utils';\nimport { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface TopProductsProps {\n  products: Product[];\n  title: string;\n  description: string;\n  methodology: string;\n}\n\nexport function TopProducts({ products, title, description, methodology }: TopProductsProps) {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <StarIcon\n        key={i}\n        className={`h-4 w-4 ${\n          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  const getRankBadge = (index: number) => {\n    const badges = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'];\n    return badges[index] || `${index + 1}️⃣`;\n  };\n\n  return (\n    <article className=\"max-w-4xl mx-auto\">\n      {/* Header */}\n      <header className=\"mb-8\">\n        <div className=\"flex items-center gap-2 text-sm text-muted-foreground mb-4\">\n          <span>Best Of</span>\n          <span>•</span>\n          <span>Updated January 2025</span>\n        </div>\n        \n        <h1 className=\"text-4xl font-bold text-foreground mb-4\">{title}</h1>\n        <p className=\"text-xl text-muted-foreground mb-6\">{description}</p>\n      </header>\n\n      {/* Quick Comparison Table */}\n      <Card className=\"mb-8\">\n        <CardHeader>\n          <CardTitle>Quick Comparison</CardTitle>\n          <CardDescription>\n            Compare our top picks at a glance\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-left py-2\">Rank</th>\n                  <th className=\"text-left py-2\">Product</th>\n                  <th className=\"text-left py-2\">Type</th>\n                  <th className=\"text-left py-2\">Rating</th>\n                  <th className=\"text-left py-2\">Price</th>\n                  <th className=\"text-left py-2\">Best For</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map((product, index) => (\n                  <tr key={product.id} className=\"border-b\">\n                    <td className=\"py-3\">{getRankBadge(index)}</td>\n                    <td className=\"py-3 font-medium\">{product.name}</td>\n                    <td className=\"py-3 capitalize\">{product.type}</td>\n                    <td className=\"py-3\">\n                      <div className=\"flex items-center gap-1\">\n                        <div className=\"flex\">{renderStars(product.rating)}</div>\n                        <span className=\"ml-1\">{product.rating}</span>\n                      </div>\n                    </td>\n                    <td className=\"py-3\">{formatPrice(product.price)}</td>\n                    <td className=\"py-3 text-muted-foreground\">\n                      {index === 0 && 'Premium Quality'}\n                      {index === 1 && 'Convenience'}\n                      {index === 2 && 'Daily Wellness'}\n                      {index > 2 && 'Value'}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Detailed Reviews */}\n      <div className=\"space-y-8\">\n        {products.map((product, index) => (\n          <Card key={product.id} className=\"overflow-hidden\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 p-6\">\n              {/* Product Image */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"relative\">\n                  <div className=\"absolute -top-2 -left-2 bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold z-10\">\n                    {index + 1}\n                  </div>\n                  {product.images.length > 0 && (\n                    <Image\n                      src={product.images[0].url}\n                      alt={product.images[0].alt}\n                      width={300}\n                      height={200}\n                      className=\"rounded-lg w-full h-48 object-cover\"\n                    />\n                  )}\n                </div>\n              </div>\n\n              {/* Product Details */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-foreground mb-2\">\n                      {getRankBadge(index)} {product.name}\n                    </h3>\n                    <div className=\"flex items-center gap-4 mb-2\">\n                      <div className=\"flex items-center gap-1\">\n                        <div className=\"flex\">{renderStars(product.rating)}</div>\n                        <span className=\"font-medium\">{product.rating}</span>\n                      </div>\n                      <span className=\"text-muted-foreground\">by {product.brand}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <span className=\"text-2xl font-bold text-primary\">\n                        {formatPrice(product.price)}\n                      </span>\n                      <span className=\"text-sm text-muted-foreground uppercase\">\n                        {product.type}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Key Features */}\n                <div className=\"mb-4\">\n                  <h4 className=\"font-semibold mb-2\">Why We Recommend It:</h4>\n                  <ul className=\"space-y-1\">\n                    {product.pros.slice(0, 3).map((pro, proIndex) => (\n                      <li key={proIndex} className=\"flex items-start gap-2 text-sm\">\n                        <CheckCircleIcon className=\"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0\" />\n                        <span>{pro}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Quality Indicators */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {product.organic && (\n                    <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                      Organic\n                    </span>\n                  )}\n                  {product.thirdPartyTested && (\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                      Third-Party Tested\n                    </span>\n                  )}\n                  {product.madeInUSA && (\n                    <span className=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\n                      Made in USA\n                    </span>\n                  )}\n                  {product.nonGmo && (\n                    <span className=\"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full\">\n                      Non-GMO\n                    </span>\n                  )}\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex flex-col sm:flex-row gap-3\">\n                  <Button asChild className=\"flex-1\">\n                    <a \n                      href={product.affiliateLinks[0]?.url} \n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\"\n                      onClick={() => {\n                        // Track affiliate click\n                        fetch('/api/tracking/click', {\n                          method: 'POST',\n                          headers: { 'Content-Type': 'application/json' },\n                          body: JSON.stringify({\n                            productId: product.id,\n                            affiliateUrl: product.affiliateLinks[0]?.url,\n                            source: 'listicle-cta'\n                          })\n                        });\n                      }}\n                    >\n                      Check Price on {product.affiliateLinks[0]?.retailer}\n                    </a>\n                  </Button>\n                  <Button variant=\"outline\" asChild>\n                    <Link href={`/reviews/${product.id}`}>\n                      Read Full Review\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </Card>\n        ))}\n      </div>\n\n      {/* Methodology */}\n      <Card className=\"mt-12\">\n        <CardHeader>\n          <CardTitle>Our Testing Methodology</CardTitle>\n          <CardDescription>\n            How we evaluate and rank moringa supplements\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"prose prose-sm max-w-none\">\n            <div dangerouslySetInnerHTML={{ __html: methodology }} />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Affiliate Disclosure */}\n      <div className=\"mt-8 p-4 bg-muted/50 rounded-lg border\">\n        <p className=\"text-sm text-muted-foreground\">\n          <strong>Affiliate Disclosure:</strong> Plant Based Vitality may earn a commission \n          if you purchase products through our affiliate links. This doesn't affect our \n          editorial independence or the price you pay. We only recommend products we \n          genuinely believe in based on our testing and research.\n        </p>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AAiBO,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAoB;IACzF,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,6MAAA,CAAA,WAAQ;gBAEP,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,oBAAoB,iBAC7C;eAHG;;;;;IAMX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YAAC;YAAM;YAAM;YAAM;YAAO;SAAM;QAC/C,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,QAAQ,EAAE,EAAE,CAAC;IAC1C;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;0BAIrD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAiB;;;;;;;;;;;;;;;;;kDAGnC,8OAAC;kDACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;kEAAQ,aAAa;;;;;;kEACnC,8OAAC;wDAAG,WAAU;kEAAoB,QAAQ,IAAI;;;;;;kEAC9C,8OAAC;wDAAG,WAAU;kEAAmB,QAAQ,IAAI;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAQ,YAAY,QAAQ,MAAM;;;;;;8EACjD,8OAAC;oEAAK,WAAU;8EAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;kEAG1C,8OAAC;wDAAG,WAAU;kEAAQ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;kEAC/C,8OAAC;wDAAG,WAAU;;4DACX,UAAU,KAAK;4DACf,UAAU,KAAK;4DACf,UAAU,KAAK;4DACf,QAAQ,KAAK;;;;;;;;+CAfT,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0B/B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAkB,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ;;;;;;4CAEV,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gDAC1B,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gDAC1B,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;8CAOlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DACX,aAAa;4DAAO;4DAAE,QAAQ,IAAI;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAQ,YAAY,QAAQ,MAAM;;;;;;kFACjD,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,MAAM;;;;;;;;;;;;0EAE/C,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAI,QAAQ,KAAK;;;;;;;;;;;;;kEAE3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;0EACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAOrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,yBAClC,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC,2NAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,8OAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;sDASf,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBACd,8OAAC;oDAAK,WAAU;8DAA6D;;;;;;gDAI9E,QAAQ,gBAAgB,kBACvB,8OAAC;oDAAK,WAAU;8DAA2D;;;;;;gDAI5E,QAAQ,SAAS,kBAChB,8OAAC;oDAAK,WAAU;8DAAyD;;;;;;gDAI1E,QAAQ,MAAM,kBACb,8OAAC;oDAAK,WAAU;8DAA+D;;;;;;;;;;;;sDAOnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,WAAU;8DACxB,cAAA,8OAAC;wDACC,MAAM,QAAQ,cAAc,CAAC,EAAE,EAAE;wDACjC,QAAO;wDACP,KAAI;wDACJ,SAAS;4DACP,wBAAwB;4DACxB,MAAM,uBAAuB;gEAC3B,QAAQ;gEACR,SAAS;oEAAE,gBAAgB;gEAAmB;gEAC9C,MAAM,KAAK,SAAS,CAAC;oEACnB,WAAW,QAAQ,EAAE;oEACrB,cAAc,QAAQ,cAAc,CAAC,EAAE,EAAE;oEACzC,QAAQ;gEACV;4DACF;wDACF;;4DACD;4DACiB,QAAQ,cAAc,CAAC,EAAE,EAAE;;;;;;;;;;;;8DAG/C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,OAAO;8DAC/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA1GrC,QAAQ,EAAE;;;;;;;;;;0BAsHzB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,yBAAyB;oCAAE,QAAQ;gCAAY;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAA8B;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}]}