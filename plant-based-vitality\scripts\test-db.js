const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function testDatabase() {
  try {
    console.log('🔍 Connecting to database...');
    
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected successfully!');
    
    // List all tables
    console.log('\n📋 Current tables in database:');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log('   No tables found');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
    }
    
    // Show actual data from each table
    console.log('\n📊 Table contents:');
    for (const row of tablesResult.rows) {
      const tableName = row.table_name;
      try {
        const dataResult = await client.query(`SELECT * FROM "${tableName}" LIMIT 10`);
        console.log(`\n   📋 ${tableName} (${dataResult.rows.length} rows shown, max 10):`);

        if (dataResult.rows.length === 0) {
          console.log('      (empty table)');
        } else {
          // Show column names
          const columns = Object.keys(dataResult.rows[0]);
          console.log(`      Columns: ${columns.join(', ')}`);

          // Show each row
          dataResult.rows.forEach((row, index) => {
            console.log(`      Row ${index + 1}:`);
            columns.forEach(col => {
              let value = row[col];
              if (typeof value === 'object' && value !== null) {
                value = JSON.stringify(value);
              }
              if (typeof value === 'string' && value.length > 100) {
                value = value.substring(0, 100) + '...';
              }
              console.log(`        ${col}: ${value}`);
            });
            console.log('');
          });
        }
      } catch (error) {
        console.log(`   ${tableName}: Error reading data - ${error.message}`);
      }
    }
    
    client.release();
    console.log('\n✅ Database test completed');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  } finally {
    await pool.end();
  }
}

testDatabase();
