const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function testDatabase() {
  try {
    console.log('🔍 Connecting to database...');
    
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected successfully!');

    // Show current database
    const dbResult = await client.query('SELECT current_database()');
    console.log(`📍 Current database: ${dbResult.rows[0].current_database}`);
    
    // List all schemas first
    console.log('\n📋 Available schemas:');
    const schemasResult = await client.query(`
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY schema_name;
    `);

    schemasResult.rows.forEach(row => {
      console.log(`   - ${row.schema_name}`);
    });

    // List all tables in all schemas
    console.log('\n📋 Current tables in database:');
    const tablesResult = await client.query(`
      SELECT table_schema, table_name
      FROM information_schema.tables
      WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY table_schema, table_name;
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log('   No tables found');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`   - ${row.table_schema}.${row.table_name}`);
      });
    }
    
    // Show actual data from each table
    console.log('\n📊 Table contents:');
    for (const row of tablesResult.rows) {
      const schemaName = row.table_schema;
      const tableName = row.table_name;
      const fullTableName = `"${schemaName}"."${tableName}"`;

      try {
        const dataResult = await client.query(`SELECT * FROM ${fullTableName} LIMIT 10`);
        console.log(`\n   📋 ${schemaName}.${tableName} (${dataResult.rows.length} rows shown, max 10):`);

        if (dataResult.rows.length === 0) {
          console.log('      (empty table)');
        } else {
          // Show column names
          const columns = Object.keys(dataResult.rows[0]);
          console.log(`      Columns: ${columns.join(', ')}`);

          // Show each row
          dataResult.rows.forEach((row, index) => {
            console.log(`      Row ${index + 1}:`);
            columns.forEach(col => {
              let value = row[col];
              if (typeof value === 'object' && value !== null) {
                value = JSON.stringify(value);
              }
              if (typeof value === 'string' && value.length > 100) {
                value = value.substring(0, 100) + '...';
              }
              console.log(`        ${col}: ${value}`);
            });
            console.log('');
          });
        }
      } catch (error) {
        console.log(`   ${schemaName}.${tableName}: Error reading data - ${error.message}`);
      }
    }
    
    client.release();
    console.log('\n✅ Database test completed');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  } finally {
    await pool.end();
  }
}

testDatabase();
