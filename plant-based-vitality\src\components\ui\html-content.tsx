import { cn } from '@/lib/utils';

interface HtmlContentProps {
  content: string;
  className?: string;
}

/**
 * Component for rendering HTML content with proper styling
 * Automatically applies review-content styling for consistent typography
 */
export function HtmlContent({ content, className }: HtmlContentProps) {
  return (
    <div 
      className={cn(
        "review-content space-y-4 text-foreground leading-relaxed",
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }} 
    />
  );
}
