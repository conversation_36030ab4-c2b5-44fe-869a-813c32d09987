# MVP Implementation Plan: Moringa Supplements Affiliate Website

## **Tech Stack & Project Setup (Week 1)**

### **Framework & Tools**
- Next.js (TypeScript)
- Tailwind CSS
- Payload CMS (optional: replace with SaaS CMS for speed)
- TrackBee (Affiliate Click Tracking)
- ConvertKit (only basic opt-in form)

```bash
npx create-next-app@latest moringa-hub --typescript --tailwind --eslint --app
cd moringa-hub

npm install next-seo @headlessui/react @heroicons/react react-hook-form
npm install @payloadcms/payload (optional)
npm install @payloadcms/db-mongodb (optional)
npm install @payloadcms/richtext-slate (optional)
```

## **Content Strategy: Focus Exclusively on Moringa (Week 2 & 3)**

### **Content Types**
- **3 Product Reviews**
  - Best Moringa Powder
  - Best Moringa Capsules
  - Best Moringa Tea

- **1 Listicle**
  - "Top 5 Moringa Supplements for 2025"

- **1 Comparison Guide**
  - "Moringa Powder vs Capsules: Which is Better?"

- **1 Educational Article (optional)**
  - "What is Moringa? Benefits and Uses"

### **SEO Focus Keywords**
- "best Moringa supplement"
- "Moringa powder review"
- "Moringa capsules buy"
- "Moringa benefits"
- "Moringa powder vs capsules"

## **Simplified Content Structure (Payload or SaaS CMS)**
- **Products**
  - Name
  - Image
  - Price
  - Key Benefits
  - Affiliate Link

- **Reviews**
  - Product Relation
  - Rating
  - Pros/Cons
  - Rich Text Content

- **Listicles**
  - Title
  - Introduction
  - Array of Products (with reason for selection)
  - Conclusion

- **Opt-in Form**
  - Lead magnet placeholder (email funnel to be added later)

## **Tracking & Analytics (Week 3)**
- Integrate TrackBee for affiliate clicks.
- Google Analytics 4 (optional).

## **Deployment (Week 4)**
- Deploy on Vercel or Netlify.
- Simple homepage:
  - Hero Text
  - Navigation to Moringa category page
  - Call-to-Action Opt-in Block

- Publish all content:
  - 3 reviews
  - 1 listicle
  - 1 comparison guide

- FTC Disclosure visible at the top of each review.

## **Priorities**
- Fast, functional website
- Solid SEO basics
- Simple opt-in form active
- Affiliate tracking operational
- First content published

## **4-Week Timeline**
| Week | Tasks                                          |
|------|------------------------------------------------|
| Week 1 | Set up tech stack + use AI Agent for code generation and payload schemas. Build basic layout. |
| Week 2 | AI Agent generates reviews, listicle, and comparison. Publish 3-5 key pages. |
| Week 3 | Add tracking, SEO metadata, and affiliate disclosures. Conduct testing. |
| Week 4 | Optimize, test on mobile, and launch. |

## **AI Agent Tasks**
- Generate Payload schemas (if used)
- Generate Next.js components (Opt-in Form, ProductCard, ListicleItem)
- Write and optimize content (via prompts)
- Generate SEO metadata templates
- Generate tracking code

---

**A separate document will outline Phase 2 for expanding with email funnels, additional content, and broader marketing.**
