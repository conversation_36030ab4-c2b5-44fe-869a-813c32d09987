# HTML Content Styling Guide

## Overview

This project uses a consistent styling system for HTML content that is rendered via `dangerouslySetInnerHTML`. This ensures all HTML content (reviews, methodology, articles, etc.) has proper typography and formatting.

## Using the HtmlContent Component

**Always use the `HtmlContent` component instead of directly using `dangerouslySetInnerHTML`:**

```tsx
import { HtmlContent } from '@/components/ui/html-content';

// ✅ Correct - Use HtmlContent component
<HtmlContent content={htmlString} />

// ❌ Incorrect - Don't use dangerouslySetInnerHTML directly
<div dangerouslySetInnerHTML={{ __html: htmlString }} />
```

## Custom Styling Classes

If you need to add custom styling, you can pass additional classes:

```tsx
<HtmlContent 
  content={htmlString} 
  className="custom-class another-class" 
/>
```

## CSS Styling System

The styling is handled by the `.review-content` CSS class in `globals.css`. This provides:

### Typography
- **Headings (h1-h6)**: Proper font weights, sizes, and spacing
- **Paragraphs**: Good line height and margin bottom
- **Lists**: Proper indentation and list-style-types
- **Strong/Bold**: Correct font weight
- **Emphasis/Italic**: Proper font style

### Colors
- All text uses CSS variables for consistent theming
- Supports both light and dark modes automatically
- Uses `var(--foreground)` for main text
- Uses `var(--muted-foreground)` for secondary text

### Spacing
- Consistent margins and padding
- Proper vertical rhythm between elements
- Responsive spacing that works on all devices

## Supported HTML Elements

The styling system supports these HTML elements:

- `<h1>` to `<h6>` - Headings with proper hierarchy
- `<p>` - Paragraphs with good line height
- `<ul>` and `<ol>` - Unordered and ordered lists
- `<li>` - List items with proper spacing
- `<strong>` and `<b>` - Bold text
- `<em>` and `<i>` - Italic text
- `<blockquote>` - Quoted text with border styling

## Examples

### Review Content
```tsx
const reviewContent = `
<h2>Product Overview</h2>
<p>This is a comprehensive review of the product...</p>
<ul>
<li>Feature 1</li>
<li>Feature 2</li>
</ul>
`;

<HtmlContent content={reviewContent} />
```

### Methodology Content
```tsx
const methodology = `
<h3>Testing Process</h3>
<p>Our testing methodology includes:</p>
<ol>
<li>Laboratory analysis</li>
<li>Expert evaluation</li>
<li>Consumer testing</li>
</ol>
`;

<HtmlContent content={methodology} />
```

## Migration Guide

If you have existing code using `dangerouslySetInnerHTML`, migrate it like this:

### Before
```tsx
<div className="prose prose-sm max-w-none">
  <div dangerouslySetInnerHTML={{ __html: content }} />
</div>
```

### After
```tsx
<HtmlContent content={content} />
```

## Best Practices

1. **Always use HtmlContent**: Never use `dangerouslySetInnerHTML` directly
2. **Consistent HTML structure**: Use proper semantic HTML in your content strings
3. **Test both themes**: Verify content looks good in both light and dark modes
4. **Mobile responsive**: The styling is responsive by default
5. **Accessibility**: Use proper heading hierarchy (h1 → h2 → h3, etc.)

## Troubleshooting

### Content not styled properly
- Make sure you're using the `HtmlContent` component
- Check that your HTML string contains valid HTML elements
- Verify the CSS is loaded (check browser dev tools)

### Dark mode issues
- The styling uses CSS variables that automatically adapt to theme changes
- If colors look wrong, check that you're not overriding with hardcoded colors

### Spacing issues
- The `.review-content` class provides consistent spacing
- Avoid adding custom margin/padding that conflicts with the base styling
