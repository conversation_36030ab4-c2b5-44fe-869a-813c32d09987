{"mappings": "AAAA;;;;;;;;;;CAUC,GAIM,SAAS,0CAAU,OAAoB,EAAE,OAAiB,EAAE,cAA2B,YAAY;IACxG,IAAI,OAAO,QAAQ,qBAAqB;IACxC,IAAI,SACF,OAAO,gBAAgB,eAAe,KAAK,KAAK,GAAG,KAAK,MAAM;IAEhE,OAAO,gBAAgB,eAAe,KAAK,IAAI,GAAG,KAAK,GAAG;AAC5D", "sources": ["packages/@react-aria/utils/src/getOffset.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Orientation} from '@react-types/shared';\n\nexport function getOffset(element: HTMLElement, reverse?: boolean, orientation: Orientation = 'horizontal'): number {\n  let rect = element.getBoundingClientRect();\n  if (reverse) {\n    return orientation === 'horizontal' ? rect.right : rect.bottom;\n  }\n  return orientation === 'horizontal' ? rect.left : rect.top;\n}\n"], "names": [], "version": 3, "file": "getOffset.module.js.map"}