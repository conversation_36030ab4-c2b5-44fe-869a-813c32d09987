{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;;;;;AAuBlH,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,IAAI;AAIlB,IAAI,4CAA0B,IAAI,OAAmC,yFAAyF;AACrK,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAE/B,sEAAsE;AACtE,MAAM,iDAA2B;IAC/B,KAAK;IACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,QAAkB,EAAE,CAAe;IAChE,KAAK,IAAI,WAAW,qCAClB,QAAQ,UAAU;AAEtB;AAEA;;CAEC,GACD,SAAS,iCAAW,CAAgB;IAClC,gFAAgF;IAChF,OAAO,CAAE,CAAA,EAAE,OAAO,IAAK,CAAC,CAAA,GAAA,YAAI,OAAO,EAAE,MAAM,IAAK,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,MAAK;AAC1H;AAGA,SAAS,0CAAoB,CAAgB;IAC3C,4CAAsB;IACtB,IAAI,iCAAW,IAAI;QACjB,wCAAkB;QAClB,4CAAsB,YAAY;IACpC;AACF;AAEA,SAAS,yCAAmB,CAA4B;IACtD,wCAAkB;IAClB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,eAAe;QACtD,4CAAsB;QACtB,4CAAsB,WAAW;IACnC;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,IAAI,CAAA,GAAA,qBAAa,EAAE,IAAI;QACrB,4CAAsB;QACtB,wCAAkB;IACpB;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,kFAAkF;IAClF,kFAAkF;IAClF,wCAAwC;IACxC,IAAI,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,YAAY,CAAA,GAAA,yCAAe,KAAK,CAAC,EAAE,SAAS,EAClF;IAGF,qGAAqG;IACrG,0FAA0F;IAC1F,IAAI,CAAC,6CAAuB,CAAC,gDAA0B;QACrD,wCAAkB;QAClB,4CAAsB,WAAW;IACnC;IAEA,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA,SAAS;IACP,IAAI,GAAA,2CACF;IAGF,6FAA6F;IAC7F,8DAA8D;IAC9D,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA;;CAEC,GACD,SAAS,6CAAuB,OAA4B;IAC1D,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,0CAAwB,GAAG,CAAC,CAAA,GAAA,qBAAa,EAAE,WACjH;IAGF,MAAM,eAAe,CAAA,GAAA,qBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;IAExC,0EAA0E;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,6DAA6D;IAC7D,IAAI,QAAQ,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK;IACpD,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;QACzC,4CAAsB;QACtB,MAAM,KAAK,CAAC,IAAI,EAAE;IACpB;IAEA,eAAe,gBAAgB,CAAC,WAAW,2CAAqB;IAChE,eAAe,gBAAgB,CAAC,SAAS,2CAAqB;IAC9D,eAAe,gBAAgB,CAAC,SAAS,wCAAkB;IAE3D,iEAAiE;IACjE,+DAA+D;IAC/D,aAAa,gBAAgB,CAAC,SAAS,wCAAkB;IACzD,aAAa,gBAAgB,CAAC,QAAQ,wCAAkB;IAExD,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;IACnE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;QAC1C,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;QACjE,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;QACjE,eAAe,gBAAgB,CAAC,WAAW,0CAAoB;IACjE;IAEA,sBAAsB;IACtB,aAAa,gBAAgB,CAAC,gBAAgB;QAC5C,kDAA4B;IAC9B,GAAG;QAAC,MAAM;IAAI;IAEd,0CAAwB,GAAG,CAAC,cAAc;eAAC;IAAK;AAClD;AAEA,MAAM,oDAA8B,CAAC,SAAS;IAC5C,MAAM,eAAe,CAAA,GAAA,qBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;IACxC,IAAI,cACF,eAAe,mBAAmB,CAAC,oBAAoB;IAEzD,IAAI,CAAC,0CAAwB,GAAG,CAAC,eAC/B;IAEF,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,0CAAwB,GAAG,CAAC,cAAe,KAAK;IAE3F,eAAe,mBAAmB,CAAC,WAAW,2CAAqB;IACnE,eAAe,mBAAmB,CAAC,SAAS,2CAAqB;IACjE,eAAe,mBAAmB,CAAC,SAAS,wCAAkB;IAE9D,aAAa,mBAAmB,CAAC,SAAS,wCAAkB;IAC5D,aAAa,mBAAmB,CAAC,QAAQ,wCAAkB;IAE3D,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;IACtE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;QAC1C,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;QACpE,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;QACpE,eAAe,mBAAmB,CAAC,WAAW,0CAAoB;IACpE;IAEA,0CAAwB,MAAM,CAAC;AACjC;AAmBO,SAAS,0CAAuB,OAA4B;IACjE,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;IACxC,IAAI;IACJ,IAAI,eAAe,UAAU,KAAK,WAChC,6CAAuB;SAClB;QACL,eAAe;YACb,6CAAuB;QACzB;QACA,eAAe,gBAAgB,CAAC,oBAAoB;IACtD;IAEA,OAAO,IAAM,kDAA4B,SAAS;AACpD;AAEA,kEAAkE;AAClE,iDAAiD;AACjD,IAAI,OAAO,aAAa,aACtB;AAMK,SAAS;IACd,OAAO,0CAAoB;AAC7B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,0CAAuB,QAAkB;IACvD,wCAAkB;IAClB,4CAAsB,UAAU;AAClC;AAKO,SAAS;IACd;IAEA,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,eAAO,EAAE;IACvC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;QACd;QAEA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,GAAG,EAAE;IAEL,OAAO,CAAA,GAAA,eAAO,MAAM,OAAO;AAC7B;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED;;;CAGC,GACD,SAAS,2CAAqB,WAAoB,EAAE,QAAkB,EAAE,CAAe;IACrF,IAAI,YAAW,CAAA,GAAA,uBAAe,EAAE,cAAA,wBAAA,EAAG,MAAM;IACzC,MAAM,oBAAoB,OAAO,WAAW,cAAc,CAAA,GAAA,qBAAa,EAAE,cAAA,wBAAA,EAAG,MAAM,EAAa,gBAAgB,GAAG;IAClH,MAAM,uBAAuB,OAAO,WAAW,cAAc,CAAA,GAAA,qBAAa,EAAE,cAAA,wBAAA,EAAG,MAAM,EAAa,mBAAmB,GAAG;IACxH,MAAM,eAAe,OAAO,WAAW,cAAc,CAAA,GAAA,qBAAa,EAAE,cAAA,wBAAA,EAAG,MAAM,EAAa,WAAW,GAAG;IACxG,MAAM,iBAAiB,OAAO,WAAW,cAAc,CAAA,GAAA,qBAAa,EAAE,cAAA,wBAAA,EAAG,MAAM,EAAa,aAAa,GAAG;IAE5G,qKAAqK;IACrK,kIAAkI;IAClI,cAAc,eACX,UAAS,aAAa,YAAY,qBAAqB,CAAC,wCAAkB,GAAG,CAAC,UAAS,aAAa,CAAC,IAAI,KAC1G,UAAS,aAAa,YAAY,wBACjC,UAAS,aAAa,YAAY,gBAAgB,UAAS,aAAa,CAAC,iBAAiB;IAC7F,OAAO,CAAE,CAAA,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,8CAAwB,CAAC,EAAE,GAAG,CAAC,AAAD;AACnH;AAKO,SAAS,0CAAgB,QAA2B,CAAC,CAAC;IAC3D,IAAI,eAAC,WAAW,aAAE,SAAS,EAAC,GAAG;IAC/B,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,GAAA,eAAO,EAAE,aAAa;IACnE,0CAAwB,CAAC;QACvB,gBAAgB;IAClB,GAAG;QAAC;KAAY,EAAE;qBAAC;IAAW;IAE9B,OAAO;QAAC,gBAAgB;IAAmB;AAC7C;AAKO,SAAS,0CAAwB,EAAuB,EAAE,IAAwB,EAAE,IAA8B;IACvH;IAEA,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,UAAU,CAAC,UAAoB;YACjC,0GAA0G;YAC1G,IAAI,CAAC,2CAAqB,CAAC,EAAE,iBAAA,2BAAA,KAAM,WAAW,GAAG,UAAU,IACzD;YAEF,GAAG;QACL;QACA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,uDAAuD;IACvD,GAAG;AACL", "sources": ["packages/@react-aria/interactions/src/useFocusVisible.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n"], "names": [], "version": 3, "file": "useFocusVisible.module.js.map"}