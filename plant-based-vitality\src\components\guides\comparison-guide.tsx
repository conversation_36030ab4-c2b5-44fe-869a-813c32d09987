import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ComparisonItem {
  title: string;
  description: string;
  pros: string[];
  cons: string[];
  bestFor: string[];
  priceRange: string;
  potency: 'Low' | 'Medium' | 'High';
  convenience: 'Low' | 'Medium' | 'High';
  bioavailability: 'Low' | 'Medium' | 'High';
}

interface ComparisonGuideProps {
  title: string;
  description: string;
  items: ComparisonItem[];
  winner: {
    category: string;
    item: string;
    reason: string;
  }[];
  conclusion: string;
}

export function ComparisonGuide({ title, description, items, winner, conclusion }: ComparisonGuideProps) {
  const getScoreColor = (score: 'Low' | 'Medium' | 'High') => {
    switch (score) {
      case 'High': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Low': return 'text-red-600 bg-red-100';
    }
  };

  return (
    <article className="max-w-6xl mx-auto">
      {/* Header */}
      <header className="mb-8">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
          <span>Comparison Guide</span>
          <span>•</span>
          <span>Updated January 2025</span>
        </div>
        
        <h1 className="text-4xl font-bold text-foreground mb-4">{title}</h1>
        <p className="text-xl text-muted-foreground mb-6">{description}</p>
      </header>

      {/* Quick Comparison Table */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Quick Comparison Overview</CardTitle>
          <CardDescription>
            Compare key factors at a glance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 font-semibold">Factor</th>
                  {items.map((item, index) => (
                    <th key={index} className="text-left py-3 font-semibold">
                      {item.title}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-3 font-medium">Price Range</td>
                  {items.map((item, index) => (
                    <td key={index} className="py-3">{item.priceRange}</td>
                  ))}
                </tr>
                <tr className="border-b">
                  <td className="py-3 font-medium">Potency</td>
                  {items.map((item, index) => (
                    <td key={index} className="py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.potency)}`}>
                        {item.potency}
                      </span>
                    </td>
                  ))}
                </tr>
                <tr className="border-b">
                  <td className="py-3 font-medium">Convenience</td>
                  {items.map((item, index) => (
                    <td key={index} className="py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.convenience)}`}>
                        {item.convenience}
                      </span>
                    </td>
                  ))}
                </tr>
                <tr className="border-b">
                  <td className="py-3 font-medium">Bioavailability</td>
                  {items.map((item, index) => (
                    <td key={index} className="py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.bioavailability)}`}>
                        {item.bioavailability}
                      </span>
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {items.map((item, index) => (
          <Card key={index} className="h-fit">
            <CardHeader>
              <CardTitle className="text-2xl">{item.title}</CardTitle>
              <CardDescription>{item.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Pros */}
              <div>
                <h4 className="font-semibold text-green-600 mb-3 flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5" />
                  Advantages
                </h4>
                <ul className="space-y-2">
                  {item.pros.map((pro, proIndex) => (
                    <li key={proIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Cons */}
              <div>
                <h4 className="font-semibold text-red-600 mb-3 flex items-center gap-2">
                  <XCircleIcon className="h-5 w-5" />
                  Disadvantages
                </h4>
                <ul className="space-y-2">
                  {item.cons.map((con, conIndex) => (
                    <li key={conIndex} className="flex items-start gap-2 text-sm">
                      <XCircleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <span>{con}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Best For */}
              <div>
                <h4 className="font-semibold mb-3">Best For:</h4>
                <ul className="space-y-1">
                  {item.bestFor.map((use, useIndex) => (
                    <li key={useIndex} className="text-sm text-muted-foreground">
                      • {use}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Metrics */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-xs text-muted-foreground mb-1">Potency</div>
                  <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.potency)}`}>
                    {item.potency}
                  </span>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground mb-1">Convenience</div>
                  <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.convenience)}`}>
                    {item.convenience}
                  </span>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground mb-1">Absorption</div>
                  <span className={`px-2 py-1 rounded-full text-xs ${getScoreColor(item.bioavailability)}`}>
                    {item.bioavailability}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Winners by Category */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Winners by Category</CardTitle>
          <CardDescription>
            Our recommendations for different use cases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {winner.map((win, index) => (
              <div key={index} className="text-center p-4 bg-primary/5 rounded-lg border border-primary/20">
                <div className="text-2xl mb-2">🏆</div>
                <h4 className="font-semibold text-primary mb-2">{win.category}</h4>
                <p className="font-medium mb-2">{win.item}</p>
                <p className="text-sm text-muted-foreground">{win.reason}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Conclusion */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Bottom Line</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose prose-lg max-w-none">
            <p>{conclusion}</p>
          </div>
        </CardContent>
      </Card>

      {/* CTA */}
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-4">Ready to Choose Your Moringa?</h3>
        <p className="text-muted-foreground mb-6">
          Browse our detailed reviews to find the perfect moringa supplement for your needs.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <a href="/best-moringa-supplements">View Top Rated Products</a>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <a href="/reviews">Browse All Reviews</a>
          </Button>
        </div>
      </div>

      {/* Affiliate Disclosure */}
      <div className="mt-12 p-4 bg-muted/50 rounded-lg border">
        <p className="text-sm text-muted-foreground">
          <strong>Affiliate Disclosure:</strong> Plant Based Vitality may earn a commission 
          if you purchase products through our affiliate links. This doesn't affect our 
          editorial independence or the price you pay. We only recommend products we 
          genuinely believe in based on our testing and research.
        </p>
      </div>
    </article>
  );
}
