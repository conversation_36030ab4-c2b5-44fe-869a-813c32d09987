{"mappings": ";;;AAEO,SAAS,0CAAiB,EAAkB;IACjD,IAAI,OAAO,0CAA2B,CAAA,GAAA,uBAAe,EAAE;IACvD,IAAI,SAAS,IAAI;QACf,IAAI,MACF,0CAAoB,MAAM;QAE5B,IAAI,IACF,0CAAqB,IAAI;IAE7B;AACF;AAEO,SAAS,0CAAoB,IAAa,EAAE,EAAkB;IACnE,KAAK,aAAa,CAAC,IAAI,WAAW,QAAQ;QAAC,eAAe;IAAE;IAC5D,KAAK,aAAa,CAAC,IAAI,WAAW,YAAY;QAAC,SAAS;QAAM,eAAe;IAAE;AACjF;AAEO,SAAS,0CAAqB,EAAW,EAAE,IAAoB;IACpE,GAAG,aAAa,CAAC,IAAI,WAAW,SAAS;QAAC,eAAe;IAAI;IAC7D,GAAG,aAAa,CAAC,IAAI,WAAW,WAAW;QAAC,SAAS;QAAM,eAAe;IAAI;AAChF;AAEO,SAAS,0CAA2B,QAAkB;IAC3D,IAAI,gBAAgB,CAAA,GAAA,uBAAe,EAAE;IACrC,IAAI,mBAAmB,0BAAA,oCAAA,cAAe,YAAY,CAAC;IACnD,IAAI,kBACF,OAAO,SAAS,cAAc,CAAC,qBAAqB;IAGtD,OAAO;AACT", "sources": ["packages/@react-aria/focus/src/virtualFocus.ts"], "sourcesContent": ["import {getActiveElement, getOwnerDocument} from '@react-aria/utils';\n\nexport function moveVirtualFocus(to: Element | null): void {\n  let from = getVirtuallyFocusedElement(getOwnerDocument(to));\n  if (from !== to) {\n    if (from) {\n      dispatchVirtualBlur(from, to);\n    }\n    if (to) {\n      dispatchVirtualFocus(to, from);\n    }\n  }\n}\n\nexport function dispatchVirtualBlur(from: Element, to: Element | null): void {\n  from.dispatchEvent(new FocusEvent('blur', {relatedTarget: to}));\n  from.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: to}));\n}\n\nexport function dispatchVirtualFocus(to: Element, from: Element | null): void {\n  to.dispatchEvent(new FocusEvent('focus', {relatedTarget: from}));\n  to.dispatchEvent(new FocusEvent('focusin', {bubbles: true, relatedTarget: from}));\n}\n\nexport function getVirtuallyFocusedElement(document: Document): Element | null {\n  let activeElement = getActiveElement(document);\n  let activeDescendant = activeElement?.getAttribute('aria-activedescendant');\n  if (activeDescendant) {\n    return document.getElementById(activeDescendant) || activeElement;\n  }\n\n  return activeElement;\n}\n"], "names": [], "version": 3, "file": "virtualFocus.module.js.map"}