'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ExternalLink, ShoppingCart } from 'lucide-react';

interface AffiliateLink {
  id: string;
  productId: string;
  retailer: string;
  url: string;
  price: number;
  isActive: boolean;
  priority: number;
}

interface AffiliateLinkProps {
  link: AffiliateLink;
  productName?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showPrice?: boolean;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

export function AffiliateLink({
  link,
  productName,
  className,
  variant = 'default',
  size = 'default',
  showPrice = true,
  utmSource = 'plantbasedvitality',
  utmMedium = 'affiliate',
  utmCampaign = 'product-review'
}: AffiliateLinkProps) {
  const [isTracking, setIsTracking] = useState(false);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (isTracking) return;
    
    setIsTracking(true);

    try {
      // Track the affiliate click
      const response = await fetch('/api/tracking/click', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: link.productId,
          affiliatePartner: link.retailer.toLowerCase(),
          affiliateUrl: link.url,
          pageUrl: window.location.href,
          utmSource,
          utmMedium,
          utmCampaign
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Redirect to affiliate URL
        window.open(data.redirectUrl, '_blank', 'noopener,noreferrer');
      } else {
        // Fallback: direct redirect
        window.open(link.url, '_blank', 'noopener,noreferrer');
      }
    } catch (error) {
      console.error('Tracking failed:', error);
      // Fallback: direct redirect
      window.open(link.url, '_blank', 'noopener,noreferrer');
    } finally {
      setIsTracking(false);
    }
  };

  const getRetailerIcon = (retailer: string) => {
    switch (retailer.toLowerCase()) {
      case 'amazon':
        return '🛒';
      case 'iherb':
        return '🌿';
      case 'vitacost':
        return '💊';
      default:
        return <ShoppingCart className="w-4 h-4" />;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  if (!link.isActive) {
    return null;
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleClick}
      disabled={isTracking}
    >
      <span className="flex items-center gap-2">
        {typeof getRetailerIcon(link.retailer) === 'string' ? (
          <span className="text-lg">{getRetailerIcon(link.retailer)}</span>
        ) : (
          getRetailerIcon(link.retailer)
        )}
        
        <span>
          {showPrice && (
            <span className="font-semibold">{formatPrice(link.price)}</span>
          )}
          {showPrice && ' at '}
          {link.retailer}
        </span>
        
        <ExternalLink className="w-4 h-4" />
      </span>
    </Button>
  );
}

// Bulk affiliate links component for multiple retailers
interface AffiliateLinksProps {
  links: AffiliateLink[];
  productName?: string;
  title?: string;
  className?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

export function AffiliateLinks({
  links,
  productName,
  title = "Buy Now",
  className,
  utmSource,
  utmMedium,
  utmCampaign
}: AffiliateLinksProps) {
  // Sort by priority and filter active links
  const activeLinks = links
    .filter(link => link.isActive)
    .sort((a, b) => a.priority - b.priority);

  if (activeLinks.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {title && (
        <h4 className="font-semibold text-lg text-foreground">{title}</h4>
      )}
      
      <div className="flex flex-col sm:flex-row gap-3">
        {activeLinks.map((link) => (
          <AffiliateLink
            key={link.id}
            link={link}
            productName={productName}
            variant={link.priority === 1 ? 'default' : 'outline'}
            utmSource={utmSource}
            utmMedium={utmMedium}
            utmCampaign={utmCampaign}
          />
        ))}
      </div>
      
      {/* FTC Disclosure */}
      <p className="text-xs text-muted-foreground">
        * We may earn a commission from purchases made through these links at no additional cost to you.
      </p>
    </div>
  );
}
