{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAEA,uCAAuC;;AAoBxC,oFAAoF;AACpF,sFAAsF;AACtF,oFAAoF;AACpF,MAAM,yCAAkC,EAAE;AAOnC,SAAS,0CAAU,KAAqB;IAC7C,QAAQ,IAAI,CAAC;IACb,IAAI,gBAAC,YAAY,WAAE,OAAO,eAAE,WAAW,WAAE,OAAO,UAAE,MAAM,oBAAE,gBAAgB,eAAE,WAAW,eAAE,WAAW,oBAAE,gBAAgB,oBAAE,gBAAgB,oBAAE,gBAAgB,EAAC,GAAG;IAC9J,IAAI,cAAc,CAAC,IAAM,gBAAgB,eAAe,EAAE,OAAO,GAAG,EAAE,OAAO;IAC7E,IAAI,gBAAgB,CAAC;QACnB,IAAI,kBAAkB,CAAA,GAAA,yCAAQ,EAAE,aAAa,OAAO,EAAE,SAAS;QAC/D,IAAI,cAAc,YAAY;QAC9B,IAAI,aAAa,UAAU,kBAAkB,cAAc,cAAc;QACzE,OAAO;IACT;IACA,IAAI,WAAW,CAAA,GAAA,aAAK,EAAE;IACtB,IAAI,eAAe,CAAA,GAAA,aAAK,EAAE;IAE1B,kFAAkF;IAClF,IAAI,WAAW,CAAA,GAAA,aAAK,EAAE;0BAAC;gBAAkB;IAAM;IAC/C,SAAS,OAAO,CAAC,MAAM,GAAG;IAC1B,SAAS,OAAO,CAAC,gBAAgB,GAAG;IAEpC,IAAI,iBAAiB,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,aAAa,cAAc;QAC/B,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,SAAS,OAAO,GAAG;YACnB,IAAI,SAAS,OAAO,CAAC,MAAM,EACzB,SAAS,OAAO,CAAC,MAAM,CAAC;YAE1B,IAAI,SAAS,OAAO,CAAC,gBAAgB,EACnC,SAAS,OAAO,CAAC,gBAAgB,CAAC;QAEtC;QACA,IAAI,aAAa,OAAO,KAAK,YAC3B;QAEF,aAAa,OAAO,GAAG;QACvB,IAAI,kBACF,iBAAiB;IAErB;IAEA,IAAI,YAAY,CAAC;QACf,MAAM,SAAS,EAAE,MAAM;QACvB,SAAS,OAAO,GAAG;QACnB,IAAI,aAAa,cAAc;QAC/B,IAAI,SAAS,OAAO,CAAC,MAAM,EACzB,SAAS,OAAO,CAAC,MAAM,CAAC;QAE1B,IAAI,SAAS,OAAO,CAAC,gBAAgB,EACnC,SAAS,OAAO,CAAC,gBAAgB,CAAC;QAGpC,uCAAiB,MAAM,CAAC,uCAAiB,OAAO,CAAC,SAAS;QAC1D,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,aAAa,gBAAgB;IAC1D;IAEA,IAAI,cAAc,CAAC;QACjB,MAAM,SAAS,EAAE,aAAa;QAC9B,0EAA0E;QAC1E,kEAAkE;QAClE,IAAI,uCAAiB,IAAI,CAAC,CAAA,MAAO,OAAO,QAAQ,CAAC,OAC/C;QAEF,uCAAiB,IAAI,CAAC;QACtB,OAAO,gBAAgB,CAAC,aAAa,gBAAgB;QACrD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAChD;IAEA,IAAI,eAAe;QACjB,IAAI,SACF,QAAQ;IAEZ;IAEA,IAAI,aAAa;QACf,IAAI,SACF,QAAQ;IAEZ;IAEA,IAAI,YAAY,CAAC;QACf,OAAQ,EAAE,GAAG;YACX,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,EAAE,cAAc;oBAChB,IAAI,eAAe,CAAC,SAClB;yBACK,IAAI,eAAe,SACxB;gBAEJ;gBACA;YACF,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,EAAE,cAAc;oBAChB,IAAI,eAAe,CAAC,SAClB;yBACK,IAAI,eAAe,SACxB;gBAEJ;gBACA;YACF,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,EAAE,cAAc;oBAChB,IAAI,eAAe,CAAC,SAClB;yBACK,IAAI,eAAe,SACxB;gBAEJ;gBACA;YACF,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,EAAE,cAAc;oBAChB,IAAI,eAAe,CAAC,SAClB;yBACK,IAAI,eAAe,SACxB;gBAEJ;gBACA;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,kBACF;gBAEF;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,kBACF;gBAEF;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,kBACF;gBAEF;QACJ;IACF;IAEA,OAAO;qBAAC;sBAAa;oBAAc;mBAAY;IAAS;AAC1D", "sources": ["packages/@react-aria/utils/src/useDrag1D.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n /* eslint-disable rulesdir/pure-render */\n\nimport {getOffset} from './getOffset';\nimport {Orientation} from '@react-types/shared';\nimport React, {HTMLAttributes, MutableRefObject, useRef} from 'react';\n\ninterface UseDrag1DProps {\n  containerRef: MutableRefObject<HTMLElement>,\n  reverse?: boolean,\n  orientation?: Orientation,\n  onHover?: (hovered: boolean) => void,\n  onDrag?: (dragging: boolean) => void,\n  onPositionChange?: (position: number) => void,\n  onIncrement?: () => void,\n  onDecrement?: () => void,\n  onIncrementToMax?: () => void,\n  onDecrementToMin?: () => void,\n  onCollapseToggle?: () => void\n}\n\n// Keep track of elements that we are currently handling dragging for via useDrag1D.\n// If there's an ancestor and a descendant both using useDrag1D(), and the user starts\n// dragging the descendant, we don't want useDrag1D events to fire for the ancestor.\nconst draggingElements: HTMLElement[] = [];\n\n// created for splitview, this should be reusable for things like sliders/dials\n// It also handles keyboard events on the target allowing for increment/decrement by a given stepsize as well as minifying/maximizing and toggling between minified and previous size\n// It can also take a 'reverse' param to say if we should measure from the right/bottom instead of the top/left\n// It can also handle either a vertical or horizontal movement, but not both at the same time\n\nexport function useDrag1D(props: UseDrag1DProps): HTMLAttributes<HTMLElement> {\n  console.warn('useDrag1D is deprecated, please use `useMove` instead https://react-spectrum.adobe.com/react-aria/useMove.html');\n  let {containerRef, reverse, orientation, onHover, onDrag, onPositionChange, onIncrement, onDecrement, onIncrementToMax, onDecrementToMin, onCollapseToggle} = props;\n  let getPosition = (e) => orientation === 'horizontal' ? e.clientX : e.clientY;\n  let getNextOffset = (e: MouseEvent) => {\n    let containerOffset = getOffset(containerRef.current, reverse, orientation);\n    let mouseOffset = getPosition(e);\n    let nextOffset = reverse ? containerOffset - mouseOffset : mouseOffset - containerOffset;\n    return nextOffset;\n  };\n  let dragging = useRef(false);\n  let prevPosition = useRef(0);\n\n  // Keep track of the current handlers in a ref so that the events can access them.\n  let handlers = useRef({onPositionChange, onDrag});\n  handlers.current.onDrag = onDrag;\n  handlers.current.onPositionChange = onPositionChange;\n\n  let onMouseDragged = (e: MouseEvent) => {\n    e.preventDefault();\n    let nextOffset = getNextOffset(e);\n    if (!dragging.current) {\n      dragging.current = true;\n      if (handlers.current.onDrag) {\n        handlers.current.onDrag(true);\n      }\n      if (handlers.current.onPositionChange) {\n        handlers.current.onPositionChange(nextOffset);\n      }\n    }\n    if (prevPosition.current === nextOffset) {\n      return;\n    }\n    prevPosition.current = nextOffset;\n    if (onPositionChange) {\n      onPositionChange(nextOffset);\n    }\n  };\n\n  let onMouseUp = (e: MouseEvent) => {\n    const target = e.target as HTMLElement;\n    dragging.current = false;\n    let nextOffset = getNextOffset(e);\n    if (handlers.current.onDrag) {\n      handlers.current.onDrag(false);\n    }\n    if (handlers.current.onPositionChange) {\n      handlers.current.onPositionChange(nextOffset);\n    }\n\n    draggingElements.splice(draggingElements.indexOf(target), 1);\n    window.removeEventListener('mouseup', onMouseUp, false);\n    window.removeEventListener('mousemove', onMouseDragged, false);\n  };\n\n  let onMouseDown = (e: React.MouseEvent<HTMLElement>) => {\n    const target = e.currentTarget;\n    // If we're already handling dragging on a descendant with useDrag1D, then\n    // we don't want to handle the drag motion on this target as well.\n    if (draggingElements.some(elt => target.contains(elt))) {\n      return;\n    }\n    draggingElements.push(target);\n    window.addEventListener('mousemove', onMouseDragged, false);\n    window.addEventListener('mouseup', onMouseUp, false);\n  };\n\n  let onMouseEnter = () => {\n    if (onHover) {\n      onHover(true);\n    }\n  };\n\n  let onMouseOut = () => {\n    if (onHover) {\n      onHover(false);\n    }\n  };\n\n  let onKeyDown = (e) => {\n    switch (e.key) {\n      case 'Left':\n      case 'ArrowLeft':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onDecrement && !reverse) {\n            onDecrement();\n          } else if (onIncrement && reverse) {\n            onIncrement();\n          }\n        }\n        break;\n      case 'Up':\n      case 'ArrowUp':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onDecrement && !reverse) {\n            onDecrement();\n          } else if (onIncrement && reverse) {\n            onIncrement();\n          }\n        }\n        break;\n      case 'Right':\n      case 'ArrowRight':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onIncrement && !reverse) {\n            onIncrement();\n          } else if (onDecrement && reverse) {\n            onDecrement();\n          }\n        }\n        break;\n      case 'Down':\n      case 'ArrowDown':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onIncrement && !reverse) {\n            onIncrement();\n          } else if (onDecrement && reverse) {\n            onDecrement();\n          }\n        }\n        break;\n      case 'Home':\n        e.preventDefault();\n        if (onDecrementToMin) {\n          onDecrementToMin();\n        }\n        break;\n      case 'End':\n        e.preventDefault();\n        if (onIncrementToMax) {\n          onIncrementToMax();\n        }\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (onCollapseToggle) {\n          onCollapseToggle();\n        }\n        break;\n    }\n  };\n\n  return {onMouseDown, onMouseEnter, onMouseOut, onKeyDown};\n}\n"], "names": [], "version": 3, "file": "useDrag1D.module.js.map"}