import { NextRequest, NextResponse } from 'next/server';
import { db, affiliateClicks, analyticsEvents, products } from '@/lib/db';
import { sql, desc, count, eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get affiliate clicks summary
    const clicksSummary = await db
      .select({
        affiliatePartner: affiliateClicks.affiliatePartner,
        totalClicks: count(affiliateClicks.id),
      })
      .from(affiliateClicks)
      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)
      .groupBy(affiliateClicks.affiliatePartner)
      .orderBy(desc(count(affiliateClicks.id)));

    // Get top products by clicks
    const topProducts = await db
      .select({
        productId: affiliateClicks.productId,
        productName: products.name,
        totalClicks: count(affiliateClicks.id),
      })
      .from(affiliateClicks)
      .leftJoin(products, eq(affiliateClicks.productId, products.id))
      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)
      .groupBy(affiliateClicks.productId, products.name)
      .orderBy(desc(count(affiliateClicks.id)))
      .limit(10);

    // Get daily clicks for chart
    const dailyClicks = await db
      .select({
        date: sql<string>`DATE(${affiliateClicks.clickedAt})`,
        clicks: count(affiliateClicks.id),
      })
      .from(affiliateClicks)
      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)
      .groupBy(sql`DATE(${affiliateClicks.clickedAt})`)
      .orderBy(sql`DATE(${affiliateClicks.clickedAt})`);

    // Get total stats
    const totalStats = await db
      .select({
        totalClicks: count(affiliateClicks.id),
      })
      .from(affiliateClicks)
      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`);

    // Get email subscribers count
    const emailStats = await db
      .select({
        totalSubscribers: count(),
      })
      .from(db.select().from(db.select().from(sql`email_subscribers`)));

    // Get recent clicks for activity feed
    const recentClicks = await db
      .select({
        id: affiliateClicks.id,
        affiliatePartner: affiliateClicks.affiliatePartner,
        productName: products.name,
        clickedAt: affiliateClicks.clickedAt,
        utmSource: affiliateClicks.utmSource,
        utmMedium: affiliateClicks.utmMedium,
      })
      .from(affiliateClicks)
      .leftJoin(products, eq(affiliateClicks.productId, products.id))
      .orderBy(desc(affiliateClicks.clickedAt))
      .limit(20);

    const dashboardData = {
      summary: {
        totalClicks: totalStats[0]?.totalClicks || 0,
        totalSubscribers: emailStats[0]?.totalSubscribers || 0,
        dateRange: `${startDate.toISOString().split('T')[0]} to ${new Date().toISOString().split('T')[0]}`,
      },
      affiliatePartners: clicksSummary,
      topProducts: topProducts.filter(p => p.productId !== null),
      dailyClicks,
      recentActivity: recentClicks,
    };

    return NextResponse.json(dashboardData);

  } catch (error) {
    console.error('❌ Analytics dashboard failed:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

// POST endpoint to record custom events
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { eventType, pageUrl, eventData } = body;

    if (!eventType) {
      return NextResponse.json(
        { error: 'Missing required field: eventType' },
        { status: 400 }
      );
    }

    // Get user info
    const userAgent = request.headers.get('user-agent') || '';
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';

    // Save analytics event
    await db.insert(analyticsEvents).values({
      eventType,
      pageUrl: pageUrl || request.headers.get('referer') || '',
      userIp,
      userAgent,
      eventData: eventData || null,
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Custom event tracking failed:', error);
    return NextResponse.json(
      { error: 'Failed to track event' },
      { status: 500 }
    );
  }
}
