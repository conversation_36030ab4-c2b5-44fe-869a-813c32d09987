const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function createSchema() {
  try {
    console.log('🔍 Connecting to database...');
    const client = await pool.connect();
    console.log('✅ Connected successfully!');
    
    // Try to create schema (might fail if no permissions)
    try {
      await client.query('CREATE SCHEMA IF NOT EXISTS plantbasedvitality;');
      console.log('✅ Schema "plantbasedvitality" created/verified');
    } catch (error) {
      console.log('⚠️  Could not create schema:', error.message);
      console.log('📝 Will try to create tables in public schema instead...');
    }
    
    // Create tables in public schema (no prefix needed)
    const schemaPrefix = '';
    
    console.log('\n📋 Creating tables...');
    
    // Products table
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${schemaPrefix}products (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          name varchar(255) NOT NULL,
          brand varchar(255) NOT NULL,
          type varchar(50) NOT NULL,
          price numeric(10, 2),
          rating numeric(3, 2),
          pros jsonb,
          cons jsonb,
          affiliate_links jsonb,
          images jsonb,
          certifications jsonb,
          description text,
          ingredients jsonb,
          serving_size varchar(100),
          servings_per_container numeric(10, 0),
          third_party_tested boolean DEFAULT false,
          organic boolean DEFAULT false,
          non_gmo boolean DEFAULT false,
          gluten_free boolean DEFAULT false,
          vegan boolean DEFAULT false,
          made_in_usa boolean DEFAULT false,
          amazon_asin varchar(20),
          slug varchar(255) NOT NULL UNIQUE,
          created_at timestamp DEFAULT now(),
          updated_at timestamp DEFAULT now()
        );
      `);
      console.log('✅ Products table created');
    } catch (error) {
      console.log('❌ Products table failed:', error.message);
    }
    
    // Affiliate clicks table
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${schemaPrefix}affiliate_clicks (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          product_id uuid REFERENCES ${schemaPrefix}products(id),
          affiliate_partner varchar(100) NOT NULL,
          click_id varchar(255) NOT NULL UNIQUE,
          user_ip inet,
          user_agent text,
          referrer text,
          utm_source varchar(100),
          utm_medium varchar(100),
          utm_campaign varchar(100),
          page_url text,
          clicked_at timestamp DEFAULT now()
        );
      `);
      console.log('✅ Affiliate clicks table created');
    } catch (error) {
      console.log('❌ Affiliate clicks table failed:', error.message);
    }
    
    // Email subscribers table
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${schemaPrefix}email_subscribers (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          email varchar(255) NOT NULL UNIQUE,
          first_name varchar(100),
          source varchar(100),
          convertkit_id varchar(100),
          subscribed_at timestamp DEFAULT now(),
          unsubscribed_at timestamp
        );
      `);
      console.log('✅ Email subscribers table created');
    } catch (error) {
      console.log('❌ Email subscribers table failed:', error.message);
    }
    
    // Analytics events table
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${schemaPrefix}analytics_events (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          event_type varchar(100) NOT NULL,
          page_url text,
          user_ip inet,
          user_agent text,
          event_data jsonb,
          created_at timestamp DEFAULT now()
        );
      `);
      console.log('✅ Analytics events table created');
    } catch (error) {
      console.log('❌ Analytics events table failed:', error.message);
    }
    
    client.release();
    console.log('\n✅ Schema creation completed');
    
  } catch (error) {
    console.error('❌ Schema creation failed:', error.message);
  } finally {
    await pool.end();
  }
}

createSchema();
