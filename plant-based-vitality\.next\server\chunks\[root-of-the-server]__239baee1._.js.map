{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, uuid, varchar, decimal, jsonb, text, timestamp, inet, boolean } from 'drizzle-orm/pg-core';\nimport { relations } from 'drizzle-orm';\n\n// Products table - stores all Moringa product information\nexport const products = pgTable('products', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  name: varchar('name', { length: 255 }).notNull(),\n  brand: varchar('brand', { length: 255 }).notNull(),\n  type: varchar('type', { length: 50 }).notNull(), // 'powder', 'capsules', 'tea', 'oil', 'liquid'\n  price: decimal('price', { precision: 10, scale: 2 }),\n  rating: decimal('rating', { precision: 3, scale: 2 }),\n  pros: jsonb('pros'), // Array of strings\n  cons: jsonb('cons'), // Array of strings\n  affiliateLinks: jsonb('affiliate_links'), // Array of affiliate link objects\n  images: jsonb('images'), // Array of image objects\n  certifications: jsonb('certifications'), // Array of certification strings\n  description: text('description'), // Rich text content\n  ingredients: jsonb('ingredients'), // Array of ingredient strings\n  servingSize: varchar('serving_size', { length: 100 }),\n  servingsPerContainer: decimal('servings_per_container', { precision: 10, scale: 0 }),\n  thirdPartyTested: boolean('third_party_tested').default(false),\n  organic: boolean('organic').default(false),\n  nonGmo: boolean('non_gmo').default(false),\n  glutenFree: boolean('gluten_free').default(false),\n  vegan: boolean('vegan').default(false),\n  madeInUSA: boolean('made_in_usa').default(false),\n  amazonASIN: varchar('amazon_asin', { length: 20 }),\n  slug: varchar('slug', { length: 255 }).notNull().unique(),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Affiliate clicks tracking - tracks every click on affiliate links\nexport const affiliateClicks = pgTable('affiliate_clicks', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  productId: uuid('product_id').references(() => products.id),\n  affiliatePartner: varchar('affiliate_partner', { length: 100 }).notNull(), // 'amazon', 'iherb', etc.\n  clickId: varchar('click_id', { length: 255 }).notNull().unique(),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  referrer: text('referrer'),\n  utmSource: varchar('utm_source', { length: 100 }),\n  utmMedium: varchar('utm_medium', { length: 100 }),\n  utmCampaign: varchar('utm_campaign', { length: 100 }),\n  pageUrl: text('page_url'),\n  clickedAt: timestamp('clicked_at').defaultNow(),\n});\n\n// Email subscribers - tracks newsletter signups\nexport const emailSubscribers = pgTable('email_subscribers', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  email: varchar('email', { length: 255 }).notNull().unique(),\n  firstName: varchar('first_name', { length: 100 }),\n  source: varchar('source', { length: 100 }), // 'homepage', 'review-page', etc.\n  convertkitId: varchar('convertkit_id', { length: 100 }),\n  subscribedAt: timestamp('subscribed_at').defaultNow(),\n  unsubscribedAt: timestamp('unsubscribed_at'),\n});\n\n// Analytics events - general event tracking\nexport const analyticsEvents = pgTable('analytics_events', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  eventType: varchar('event_type', { length: 100 }).notNull(), // 'page_view', 'affiliate_click', 'email_signup'\n  pageUrl: text('page_url'),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  eventData: jsonb('event_data'), // Additional event-specific data\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Define relationships\nexport const productsRelations = relations(products, ({ many }) => ({\n  affiliateClicks: many(affiliateClicks),\n}));\n\nexport const affiliateClicksRelations = relations(affiliateClicks, ({ one }) => ({\n  product: one(products, {\n    fields: [affiliateClicks.productId],\n    references: [products.id],\n  }),\n}));\n\n// Type exports for TypeScript\nexport type Product = typeof products.$inferSelect;\nexport type NewProduct = typeof products.$inferInsert;\nexport type AffiliateClick = typeof affiliateClicks.$inferSelect;\nexport type NewAffiliateClick = typeof affiliateClicks.$inferInsert;\nexport type EmailSubscriber = typeof emailSubscribers.$inferSelect;\nexport type NewEmailSubscriber = typeof emailSubscribers.$inferInsert;\nexport type AnalyticsEvent = typeof analyticsEvents.$inferSelect;\nexport type NewAnalyticsEvent = typeof analyticsEvents.$inferInsert;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAG,GAAG,OAAO;IAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAI,OAAO;IAAE;IAClD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,WAAW;QAAG,OAAO;IAAE;IACnD,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,QAAQ,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACd,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,aAAa,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACnB,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B;QAAE,WAAW;QAAI,OAAO;IAAE;IAClF,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,CAAC;IACxD,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACpC,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACnC,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC3C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC1C,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAG;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC1D,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;QAAE,QAAQ;IAAI,GAAG,OAAO;IACvE,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IAC9D,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAI;IACxC,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QAAE,QAAQ;IAAI;IACrD,cAAc,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU;IACnD,gBAAgB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;AAC5B;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI,GAAG,OAAO;IACzD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACjB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAClE,iBAAiB,KAAK;IACxB,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/E,SAAS,IAAI,UAAU;YACrB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,SAAS,EAAE;aAAC;QAC3B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\n// Create a connection pool for better performance\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,\n  max: 20, // Maximum number of clients in the pool\n  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds\n  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established\n});\n\n// Create Drizzle instance with schema\nexport const db = drizzle(pool, { schema });\n\n// Export schema for use in other files\nexport * from './schema';\n\n// Helper function to test database connection\nexport async function testConnection() {\n  try {\n    const client = await pool.connect();\n    const result = await client.query('SELECT NOW()');\n    client.release();\n    console.log('✅ Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('❌ Database connection failed:', error);\n    return false;\n  }\n}\n\n// Graceful shutdown\nprocess.on('SIGINT', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;;AAEA,kDAAkD;AAClD,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK,sCAAwC,0BAAgC;IAC7E,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO;;AAMlC,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;QAClC,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,sCAAsC,OAAO,IAAI,CAAC,EAAE;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,UAAU;IACnB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf;AAEA,QAAQ,EAAE,CAAC,WAAW;IACpB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/api/tracking/click/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { db, affiliateClicks, analyticsEvents } from '@/lib/db';\nimport { nanoid } from 'nanoid';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      productId,\n      affiliatePartner,\n      affiliateUrl,\n      pageUrl,\n      utmSource,\n      utmMedium,\n      utmCampaign\n    } = body;\n\n    // Validate required fields\n    if (!affiliatePartner || !affiliateUrl) {\n      return NextResponse.json(\n        { error: 'Missing required fields: affiliatePartner, affiliateUrl' },\n        { status: 400 }\n      );\n    }\n\n    // Get user info for tracking\n    const userAgent = request.headers.get('user-agent') || '';\n    const referrer = request.headers.get('referer') || '';\n    const forwardedFor = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';\n\n    // Generate unique click ID\n    const clickId = nanoid(12);\n\n    // Save affiliate click to database\n    await db.insert(affiliateClicks).values({\n      productId: productId || null,\n      affiliatePartner,\n      clickId,\n      userIp,\n      userAgent,\n      referrer,\n      utmSource: utmSource || null,\n      utmMedium: utmMedium || null,\n      utmCampaign: utmCampaign || null,\n      pageUrl: pageUrl || referrer,\n    });\n\n    // Save analytics event\n    await db.insert(analyticsEvents).values({\n      eventType: 'affiliate_click',\n      pageUrl: pageUrl || referrer,\n      userIp,\n      userAgent,\n      eventData: {\n        clickId,\n        productId,\n        affiliatePartner,\n        utmSource,\n        utmMedium,\n        utmCampaign\n      }\n    });\n\n    console.log(`✅ Affiliate click tracked: ${affiliatePartner} - ${clickId}`);\n\n    return NextResponse.json({\n      success: true,\n      clickId,\n      redirectUrl: affiliateUrl\n    });\n\n  } catch (error) {\n    console.error('❌ Affiliate click tracking failed:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to track click' },\n      { status: 500 }\n    );\n  }\n}\n\n// Simple hash function for IP privacy\nfunction hashIP(ip: string): string {\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n// Generate simple session ID\nfunction generateSessionId(): string {\n  return Math.random().toString(36).substring(2, 15) + \n         Math.random().toString(36).substring(2, 15);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AACA;;;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,SAAS,EACT,SAAS,EACT,WAAW,EACZ,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0D,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACvD,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;QACnD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,SAAS,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU,QAAQ,EAAE,IAAI;QAEtE,2BAA2B;QAC3B,MAAM,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE;QAEvB,mCAAmC;QACnC,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,kBAAe,EAAE,MAAM,CAAC;YACtC,WAAW,aAAa;YACxB;YACA;YACA;YACA;YACA;YACA,WAAW,aAAa;YACxB,WAAW,aAAa;YACxB,aAAa,eAAe;YAC5B,SAAS,WAAW;QACtB;QAEA,uBAAuB;QACvB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,kBAAe,EAAE,MAAM,CAAC;YACtC,WAAW;YACX,SAAS,WAAW;YACpB;YACA;YACA,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,iBAAiB,GAAG,EAAE,SAAS;QAEzE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,aAAa;QACf;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,sCAAsC;AACtC,SAAS,OAAO,EAAU;IACxB,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAEA,6BAA6B;AAC7B,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACjD", "debugId": null}}]}