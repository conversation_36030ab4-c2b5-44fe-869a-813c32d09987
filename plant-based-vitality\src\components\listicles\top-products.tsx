'use client';

import { StarIcon, CheckCircleIcon } from '@heroicons/react/24/solid';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { HtmlContent } from '@/components/ui/html-content';
import { formatPrice } from '@/lib/utils';
import { Product } from '@/types';
import Image from 'next/image';
import Link from 'next/link';

interface TopProductsProps {
  products: Product[];
  title: string;
  description: string;
  methodology: string;
}

export function TopProducts({ products, title, description, methodology }: TopProductsProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getRankBadge = (index: number) => {
    const badges = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'];
    return badges[index] || `${index + 1}️⃣`;
  };

  return (
    <article className="max-w-4xl mx-auto">
      {/* Header */}
      <header className="mb-8">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
          <span>Best Of</span>
          <span>•</span>
          <span>Updated January 2025</span>
        </div>
        
        <h1 className="text-4xl font-bold text-foreground mb-4">{title}</h1>
        <p className="text-xl text-muted-foreground mb-6">{description}</p>
      </header>

      {/* Quick Comparison Table */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Quick Comparison</CardTitle>
          <CardDescription>
            Compare our top picks at a glance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Rank</th>
                  <th className="text-left py-2">Product</th>
                  <th className="text-left py-2">Type</th>
                  <th className="text-left py-2">Rating</th>
                  <th className="text-left py-2">Price</th>
                  <th className="text-left py-2">Best For</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product, index) => (
                  <tr key={product.id} className="border-b">
                    <td className="py-3">{getRankBadge(index)}</td>
                    <td className="py-3 font-medium">{product.name}</td>
                    <td className="py-3 capitalize">{product.type}</td>
                    <td className="py-3">
                      <div className="flex items-center gap-1">
                        <div className="flex">{renderStars(product.rating)}</div>
                        <span className="ml-1">{product.rating}</span>
                      </div>
                    </td>
                    <td className="py-3">{formatPrice(product.price)}</td>
                    <td className="py-3 text-muted-foreground">
                      {index === 0 && 'Premium Quality'}
                      {index === 1 && 'Convenience'}
                      {index === 2 && 'Daily Wellness'}
                      {index > 2 && 'Value'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Reviews */}
      <div className="space-y-8">
        {products.map((product, index) => (
          <Card key={product.id} className="overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
              {/* Product Image */}
              <div className="lg:col-span-1">
                <div className="relative">
                  <div className="absolute -top-2 -left-2 bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold z-10">
                    {index + 1}
                  </div>
                  {product.images.length > 0 && (
                    <Image
                      src={product.images[0].url}
                      alt={product.images[0].alt}
                      width={300}
                      height={200}
                      className="rounded-lg w-full h-48 object-cover"
                    />
                  )}
                </div>
              </div>

              {/* Product Details */}
              <div className="lg:col-span-2">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-2xl font-bold text-foreground mb-2">
                      {getRankBadge(index)} {product.name}
                    </h3>
                    <div className="flex items-center gap-4 mb-2">
                      <div className="flex items-center gap-1">
                        <div className="flex">{renderStars(product.rating)}</div>
                        <span className="font-medium">{product.rating}</span>
                      </div>
                      <span className="text-muted-foreground">by {product.brand}</span>
                    </div>
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-2xl font-bold text-primary">
                        {formatPrice(product.price)}
                      </span>
                      <span className="text-sm text-muted-foreground uppercase">
                        {product.type}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Key Features */}
                <div className="mb-4">
                  <h4 className="font-semibold mb-2">Why We Recommend It:</h4>
                  <ul className="space-y-1">
                    {product.pros.slice(0, 3).map((pro, proIndex) => (
                      <li key={proIndex} className="flex items-start gap-2 text-sm">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{pro}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Quality Indicators */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {product.organic && (
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      Organic
                    </span>
                  )}
                  {product.thirdPartyTested && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Third-Party Tested
                    </span>
                  )}
                  {product.madeInUSA && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                      Made in USA
                    </span>
                  )}
                  {product.nonGmo && (
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                      Non-GMO
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button asChild className="flex-1">
                    <a 
                      href={product.affiliateLinks[0]?.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      onClick={() => {
                        // Track affiliate click
                        fetch('/api/tracking/click', {
                          method: 'POST',
                          headers: { 'Content-Type': 'application/json' },
                          body: JSON.stringify({
                            productId: product.id,
                            affiliateUrl: product.affiliateLinks[0]?.url,
                            source: 'listicle-cta'
                          })
                        });
                      }}
                    >
                      Check Price on {product.affiliateLinks[0]?.retailer}
                    </a>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href={`/reviews/${product.id}`}>
                      Read Full Review
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Methodology */}
      <Card className="mt-12">
        <CardHeader>
          <CardTitle>Our Testing Methodology</CardTitle>
          <CardDescription>
            How we evaluate and rank moringa supplements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HtmlContent content={methodology} />
        </CardContent>
      </Card>

      {/* Affiliate Disclosure */}
      <div className="mt-8 p-4 bg-muted/50 rounded-lg border">
        <p className="text-sm text-muted-foreground">
          <strong>Affiliate Disclosure:</strong> Plant Based Vitality may earn a commission 
          if you purchase products through our affiliate links. This doesn't affect our 
          editorial independence or the price you pay. We only recommend products we 
          genuinely believe in based on our testing and research.
        </p>
      </div>
    </article>
  );
}
