{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format price to USD currency\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\n/**\n * Generate slug from string\n */\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.substring(0, length).trim() + '...';\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Generate star rating display\n */\nexport function generateStarRating(rating: number): string {\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 !== 0;\n  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);\n\n  return (\n    '★'.repeat(fullStars) +\n    (hasHalfStar ? '☆' : '') +\n    '☆'.repeat(emptyStars)\n  );\n}\n\n/**\n * Validate email address\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate affiliate link with tracking parameters\n */\nexport function generateAffiliateLink(\n  baseUrl: string,\n  productId: string,\n  source?: string\n): string {\n  const url = new URL(baseUrl);\n  url.searchParams.set('utm_source', 'plantbasedvitality');\n  url.searchParams.set('utm_medium', 'affiliate');\n  url.searchParams.set('utm_campaign', productId);\n  if (source) {\n    url.searchParams.set('utm_content', source);\n  }\n  return url.toString();\n}\n\n/**\n * Hash IP address for privacy\n */\nexport function hashIP(ip: string): string {\n  // Simple hash function for privacy compliance\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n/**\n * Get reading time estimate\n */\nexport function getReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = text.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAKO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,QAAQ,IAAI,KAAK;AAC5C;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,SAAS,MAAM;IACnC,MAAM,aAAa,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;IAEvD,OACE,IAAI,MAAM,CAAC,aACX,CAAC,cAAc,MAAM,EAAE,IACvB,IAAI,MAAM,CAAC;AAEf;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,sBACd,OAAe,EACf,SAAiB,EACjB,MAAe;IAEf,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB;IACrC,IAAI,QAAQ;QACV,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe;IACtC;IACA,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,OAAO,EAAU;IAC/B,8CAA8C;IAC9C,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAKO,SAAS,eAAe,IAAY;IACzC,MAAM,iBAAiB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM;IAC1C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const baseStyles = cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n      {\n        // Variants\n        'bg-primary text-primary-foreground hover:bg-primary/90':\n          variant === 'default',\n        'bg-secondary text-secondary-foreground hover:bg-secondary/80':\n          variant === 'secondary',\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground':\n          variant === 'outline',\n        'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n        'text-primary underline-offset-4 hover:underline': variant === 'link',\n      },\n      {\n        // Sizes\n        'h-10 px-4 py-2': size === 'default',\n        'h-9 rounded-md px-3': size === 'sm',\n        'h-11 rounded-md px-8': size === 'lg',\n        'h-10 w-10': size === 'icon',\n      },\n      className\n    );\n\n    return <button className={baseStyles} ref={ref} {...props} />;\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,mQACA;QACE,WAAW;QACX,0DACE,YAAY;QACd,gEACE,YAAY;QACd,kFACE,YAAY;QACd,gDAAgD,YAAY;QAC5D,mDAAmD,YAAY;IACjE,GACA;QACE,QAAQ;QACR,kBAAkB,SAAS;QAC3B,uBAAuB,SAAS;QAChC,wBAAwB,SAAS;QACjC,aAAa,SAAS;IACxB,GACA;IAGF,qBAAO,8OAAC;QAAO,WAAW;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAC3D;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\nconst navigation = [\n  { name: 'Reviews', href: '/reviews' },\n  { name: 'Best Of', href: '/best-moringa-supplements' },\n  { name: 'Guides', href: '/guides' },\n  { name: 'About', href: '/about' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 w-full border-b border-border/40\">\n      <nav className=\"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Plant Based Vitality</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-sm\">PBV</span>\n              </div>\n              <span className=\"text-xl font-bold text-foreground\">\n                Plant Based Vitality\n              </span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-foreground\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-12\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className=\"text-sm font-semibold leading-6 text-foreground hover:text-primary transition-colors\"\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\">\n          <Button variant=\"outline\" size=\"sm\">\n            Newsletter\n          </Button>\n          <Button size=\"sm\">\n            Free Guide\n          </Button>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <div className={cn(\n        \"lg:hidden\",\n        mobileMenuOpen ? \"fixed inset-0 z-50\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-background px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-border/10\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"-m-1.5 p-1.5\" onClick={() => setMobileMenuOpen(false)}>\n              <span className=\"sr-only\">Plant Based Vitality</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-sm\">PBV</span>\n                </div>\n                <span className=\"text-xl font-bold text-foreground\">\n                  Plant Based Vitality\n                </span>\n              </div>\n            </Link>\n            <button\n              type=\"button\"\n              className=\"-m-2.5 rounded-md p-2.5 text-foreground\"\n              onClick={() => setMobileMenuOpen(false)}\n            >\n              <span className=\"sr-only\">Close menu</span>\n              <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n          <div className=\"mt-6 flow-root\">\n            <div className=\"-my-6 divide-y divide-border/10\">\n              <div className=\"space-y-2 py-6\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n              <div className=\"py-6 space-y-2\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Newsletter\n                </Button>\n                <Button className=\"w-full\">\n                  Free Guide\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAA4B;IACrD;QAAE,MAAM;QAAU,MAAM;IAAU;IAClC;QAAE,MAAM;QAAS,MAAM;IAAS;CACjC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAkE,cAAW;;kCAC1F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;0CAET,KAAK,IAAI;+BAJL,KAAK,IAAI;;;;;;;;;;kCASpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CAGpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;0BAOtB,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,iBAAiB,uBAAuB;0BAExC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;oCAAe,SAAS,IAAM,kBAAkB;;sDACvE,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;;;;;;8DAE9D,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;8CAKxD,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,eAAY;;;;;;;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAEhC,KAAK,IAAI;+CALL,KAAK,IAAI;;;;;;;;;;kDASpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAS;;;;;;0DAG7C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}]}