// Product Types
export interface Product {
  id: string;
  name: string;
  brand: string;
  type: 'powder' | 'capsules' | 'tea' | 'oil' | 'liquid';
  price: number;
  rating: number;
  pros: string[];
  cons: string[];
  affiliateLinks: AffiliateLink[];
  images: ProductImage[];
  certifications: string[];
  description: string;
  ingredients: string[];
  servingSize: string;
  servingsPerContainer: number;
  thirdPartyTested: boolean;
  organic: boolean;
  nonGmo: boolean;
  glutenFree: boolean;
  vegan: boolean;
  madeInUSA: boolean;
  amazonASIN?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Affiliate Link Types
export interface AffiliateLink {
  id: string;
  productId: string;
  retailer: string;
  url: string;
  price?: number;
  isActive: boolean;
  priority: number; // For ordering multiple links
}

// Product Image Types
export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  width: number;
  height: number;
  isPrimary: boolean;
}

// Review Types
export interface Review {
  id: string;
  title: string;
  slug: string;
  productId: string;
  content: any; // Rich text JSON
  rating: number;
  verdict: string;
  seoTitle: string;
  seoDescription: string;
  publishedAt: Date;
  authorId: string;
  featured: boolean;
  viewCount: number;
  lastReviewed: Date;
  createdAt: Date;
  updatedAt: Date;
}

// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'editor' | 'author';
  bio?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Email Subscription Types
export interface EmailSubscriber {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  subscribedAt: Date;
  isActive: boolean;
  source: string; // Where they subscribed from
  tags: string[];
}

// Affiliate Tracking Types
export interface AffiliateClick {
  id: string;
  productId: string;
  affiliateUrl: string;
  clickedAt: Date;
  userAgent: string;
  referrer: string;
  ipAddress: string; // Hashed for privacy
  sessionId: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

// SEO Types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  noindex?: boolean;
}

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  children?: NavItem[];
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

export interface EmailSignupData {
  email: string;
  firstName?: string;
}
