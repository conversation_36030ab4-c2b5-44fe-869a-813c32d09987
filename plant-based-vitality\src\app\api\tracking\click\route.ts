import { NextRequest, NextResponse } from 'next/server';
import { db, affiliateClicks, analyticsEvents } from '@/lib/db';
import { nanoid } from 'nanoid';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      productId,
      affiliatePartner,
      affiliateUrl,
      pageUrl,
      utmSource,
      utmMedium,
      utmCampaign
    } = body;

    // Validate required fields
    if (!affiliatePartner || !affiliateUrl) {
      return NextResponse.json(
        { error: 'Missing required fields: affiliatePartner, affiliateUrl' },
        { status: 400 }
      );
    }

    // Get user info for tracking
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';

    // Generate unique click ID
    const clickId = nanoid(12);

    // Save affiliate click to database
    await db.insert(affiliateClicks).values({
      productId: productId || null,
      affiliatePartner,
      clickId,
      userIp,
      userAgent,
      referrer,
      utmSource: utmSource || null,
      utmMedium: utmMedium || null,
      utmCampaign: utmCampaign || null,
      pageUrl: pageUrl || referrer,
    });

    // Save analytics event
    await db.insert(analyticsEvents).values({
      eventType: 'affiliate_click',
      pageUrl: pageUrl || referrer,
      userIp,
      userAgent,
      eventData: {
        clickId,
        productId,
        affiliatePartner,
        utmSource,
        utmMedium,
        utmCampaign
      }
    });

    console.log(`✅ Affiliate click tracked: ${affiliatePartner} - ${clickId}`);

    return NextResponse.json({
      success: true,
      clickId,
      redirectUrl: affiliateUrl
    });

  } catch (error) {
    console.error('❌ Affiliate click tracking failed:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to track click' },
      { status: 500 }
    );
  }
}

// Simple hash function for IP privacy
function hashIP(ip: string): string {
  let hash = 0;
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
}

// Generate simple session ID
function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
