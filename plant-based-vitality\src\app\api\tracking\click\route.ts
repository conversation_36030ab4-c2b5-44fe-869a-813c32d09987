import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { productId, affiliateUrl, source } = body;

    // Get user info for tracking
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';

    // Create tracking data
    const trackingData = {
      productId,
      affiliateUrl,
      source,
      userAgent,
      referrer,
      ipAddress: hashIP(ip), // Hash for privacy
      timestamp: new Date().toISOString(),
      sessionId: generateSessionId(),
    };

    // Log the click (in production, save to database)
    console.log('Affiliate click tracked:', trackingData);

    // In production, you would save this to your database
    // await saveAffiliateClick(trackingData);

    return NextResponse.json({ 
      success: true, 
      message: 'Click tracked successfully' 
    });

  } catch (error) {
    console.error('Error tracking affiliate click:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to track click' },
      { status: 500 }
    );
  }
}

// Simple hash function for IP privacy
function hashIP(ip: string): string {
  let hash = 0;
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
}

// Generate simple session ID
function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
