'use client';

import { StarIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { HtmlContent } from '@/components/ui/html-content';
import { formatPrice } from '@/lib/utils';
import { Product, AffiliateLink } from '@/types';
import Image from 'next/image';

interface ProductReviewProps {
  product: Product;
  content: string;
  verdict: string;
  lastUpdated: string;
}

export function ProductReview({ product, content, verdict, lastUpdated }: ProductReviewProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-5 w-5 ${
          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getRatingText = (rating: number) => {
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 4.0) return 'Very Good';
    if (rating >= 3.5) return 'Good';
    if (rating >= 3.0) return 'Fair';
    return 'Poor';
  };

  const primaryAffiliateLink = product.affiliateLinks.find(link => link.priority === 1) || product.affiliateLinks[0];

  return (
    <article className="max-w-4xl mx-auto">
      {/* Header Section */}
      <header className="mb-8">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
          <span>Review</span>
          <span>•</span>
          <span>Last updated: {lastUpdated}</span>
        </div>
        
        <h1 className="text-4xl font-bold text-foreground mb-4">{product.name} Review</h1>
        
        <div className="flex items-center gap-4 mb-6">
          <div className="flex items-center gap-2">
            <div className="flex">{renderStars(product.rating)}</div>
            <span className="text-lg font-semibold">{product.rating}</span>
            <span className="text-muted-foreground">({getRatingText(product.rating)})</span>
          </div>
          <div className="text-sm text-muted-foreground">
            {product.type.toUpperCase()}
          </div>
        </div>
      </header>

      {/* Quick Verdict Card */}
      <Card className="mb-8 border-primary/20 bg-primary/5">
        <CardHeader>
          <CardTitle className="text-primary">Our Verdict</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-foreground font-medium mb-4">{verdict}</p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="flex-1" asChild>
              <a 
                href={primaryAffiliateLink?.url} 
                target="_blank" 
                rel="noopener noreferrer"
                onClick={() => {
                  // Track affiliate click
                  fetch('/api/tracking/click', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      productId: product.id,
                      affiliateUrl: primaryAffiliateLink?.url,
                      source: 'review-verdict'
                    })
                  });
                }}
              >
                Check Current Price on {primaryAffiliateLink?.retailer}
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Product Image */}
          {product.images.length > 0 && (
            <div className="mb-8">
              <Image
                src={product.images[0].url}
                alt={product.images[0].alt}
                width={600}
                height={400}
                className="rounded-lg shadow-md w-full h-auto"
              />
            </div>
          )}

          {/* Review Content */}
          <div className="mb-8">
            <HtmlContent content={content} />
          </div>

          {/* Pros and Cons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  <CheckCircleIcon className="h-5 w-5" />
                  Pros
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {product.pros.map((pro, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{pro}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <XCircleIcon className="h-5 w-5" />
                  Cons
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {product.cons.map((con, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <XCircleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{con}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Product Details Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Brand</span>
                <span className="font-medium">{product.brand}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Type</span>
                <span className="font-medium capitalize">{product.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Serving Size</span>
                <span className="font-medium">{product.servingSize}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Servings</span>
                <span className="font-medium">{product.servingsPerContainer}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Price</span>
                <span className="font-medium">{formatPrice(product.price)}</span>
              </div>
              
              {/* Certifications */}
              {product.certifications.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Certifications</h4>
                  <div className="flex flex-wrap gap-2">
                    {product.certifications.map((cert, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                      >
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Quality Indicators */}
              <div className="space-y-2 pt-4 border-t">
                {product.organic && (
                  <div className="flex items-center gap-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Organic</span>
                  </div>
                )}
                {product.nonGmo && (
                  <div className="flex items-center gap-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Non-GMO</span>
                  </div>
                )}
                {product.thirdPartyTested && (
                  <div className="flex items-center gap-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Third-Party Tested</span>
                  </div>
                )}
                {product.madeInUSA && (
                  <div className="flex items-center gap-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Made in USA</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Where to Buy */}
          <Card>
            <CardHeader>
              <CardTitle>Where to Buy</CardTitle>
              <CardDescription>
                Compare prices from trusted retailers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {product.affiliateLinks.map((link, index) => (
                <Button
                  key={index}
                  variant={index === 0 ? "default" : "outline"}
                  className="w-full justify-between"
                  asChild
                >
                  <a 
                    href={link.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    onClick={() => {
                      // Track affiliate click
                      fetch('/api/tracking/click', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                          productId: product.id,
                          affiliateUrl: link.url,
                          source: 'review-sidebar'
                        })
                      });
                    }}
                  >
                    <span>{link.retailer}</span>
                    {link.price && <span>{formatPrice(link.price)}</span>}
                  </a>
                </Button>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Affiliate Disclosure */}
      <div className="mt-12 p-4 bg-muted/50 rounded-lg border">
        <p className="text-sm text-muted-foreground">
          <strong>Affiliate Disclosure:</strong> Plant Based Vitality may earn a commission 
          if you purchase products through our affiliate links. This doesn't affect our 
          editorial independence or the price you pay. We only recommend products we 
          genuinely believe in based on our testing and research.
        </p>
      </div>
    </article>
  );
}
