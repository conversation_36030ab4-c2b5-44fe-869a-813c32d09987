const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Use doadmin to grant permissions
const adminPool = new Pool({
  connectionString: process.env.DATABASE_URL.replace('plantbasedvitality:AVNS_JI55Vq9d6RZrmkqF4Qu', 'doadmin:AVNS_QIgjjyAdD2iJy0b7WWR'),
  ssl: { rejectUnauthorized: false }
});

async function grantPermissions() {
  try {
    console.log('🔍 Connecting as admin to grant permissions...');
    const client = await adminPool.connect();
    console.log('✅ Connected as admin successfully!');
    
    // Grant permissions to plantbasedvitality user
    const tables = ['products', 'affiliate_clicks', 'email_subscribers', 'analytics_events'];
    
    for (const table of tables) {
      try {
        // Grant SELECT, INSERT, UPDATE, DELETE permissions
        await client.query(`GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE ${table} TO plantbasedvitality`);
        console.log(`✅ Granted permissions on ${table} to plantbasedvitality`);
        
        // Grant USAGE on sequences (for auto-increment IDs)
        await client.query(`GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO plantbasedvitality`);
        
      } catch (error) {
        console.log(`⚠️  Permission grant failed for ${table}:`, error.message);
      }
    }
    
    // Grant schema usage
    try {
      await client.query(`GRANT USAGE ON SCHEMA public TO plantbasedvitality`);
      console.log('✅ Granted schema usage to plantbasedvitality');
    } catch (error) {
      console.log('⚠️  Schema usage grant failed:', error.message);
    }
    
    client.release();
    console.log('\n✅ Permission grants completed');
    
  } catch (error) {
    console.error('❌ Permission grant failed:', error.message);
  } finally {
    await adminPool.end();
  }
}

grantPermissions();
