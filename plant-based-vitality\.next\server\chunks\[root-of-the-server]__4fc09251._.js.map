{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, uuid, varchar, decimal, jsonb, text, timestamp, inet, boolean } from 'drizzle-orm/pg-core';\nimport { relations } from 'drizzle-orm';\n\n// Products table - stores all Moringa product information\nexport const products = pgTable('products', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  name: varchar('name', { length: 255 }).notNull(),\n  brand: varchar('brand', { length: 255 }).notNull(),\n  type: varchar('type', { length: 50 }).notNull(), // 'powder', 'capsules', 'tea', 'oil', 'liquid'\n  price: decimal('price', { precision: 10, scale: 2 }),\n  rating: decimal('rating', { precision: 3, scale: 2 }),\n  pros: jsonb('pros'), // Array of strings\n  cons: jsonb('cons'), // Array of strings\n  affiliateLinks: jsonb('affiliate_links'), // Array of affiliate link objects\n  images: jsonb('images'), // Array of image objects\n  certifications: jsonb('certifications'), // Array of certification strings\n  description: text('description'), // Rich text content\n  ingredients: jsonb('ingredients'), // Array of ingredient strings\n  servingSize: varchar('serving_size', { length: 100 }),\n  servingsPerContainer: decimal('servings_per_container', { precision: 10, scale: 0 }),\n  thirdPartyTested: boolean('third_party_tested').default(false),\n  organic: boolean('organic').default(false),\n  nonGmo: boolean('non_gmo').default(false),\n  glutenFree: boolean('gluten_free').default(false),\n  vegan: boolean('vegan').default(false),\n  madeInUSA: boolean('made_in_usa').default(false),\n  amazonASIN: varchar('amazon_asin', { length: 20 }),\n  slug: varchar('slug', { length: 255 }).notNull().unique(),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Affiliate clicks tracking - tracks every click on affiliate links\nexport const affiliateClicks = pgTable('affiliate_clicks', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  productId: uuid('product_id').references(() => products.id),\n  affiliatePartner: varchar('affiliate_partner', { length: 100 }).notNull(), // 'amazon', 'iherb', etc.\n  clickId: varchar('click_id', { length: 255 }).notNull().unique(),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  referrer: text('referrer'),\n  utmSource: varchar('utm_source', { length: 100 }),\n  utmMedium: varchar('utm_medium', { length: 100 }),\n  utmCampaign: varchar('utm_campaign', { length: 100 }),\n  pageUrl: text('page_url'),\n  clickedAt: timestamp('clicked_at').defaultNow(),\n});\n\n// Email subscribers - tracks newsletter signups\nexport const emailSubscribers = pgTable('email_subscribers', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  email: varchar('email', { length: 255 }).notNull().unique(),\n  firstName: varchar('first_name', { length: 100 }),\n  source: varchar('source', { length: 100 }), // 'homepage', 'review-page', etc.\n  convertkitId: varchar('convertkit_id', { length: 100 }),\n  subscribedAt: timestamp('subscribed_at').defaultNow(),\n  unsubscribedAt: timestamp('unsubscribed_at'),\n});\n\n// Analytics events - general event tracking\nexport const analyticsEvents = pgTable('analytics_events', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  eventType: varchar('event_type', { length: 100 }).notNull(), // 'page_view', 'affiliate_click', 'email_signup'\n  pageUrl: text('page_url'),\n  userIp: inet('user_ip'),\n  userAgent: text('user_agent'),\n  eventData: jsonb('event_data'), // Additional event-specific data\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Define relationships\nexport const productsRelations = relations(products, ({ many }) => ({\n  affiliateClicks: many(affiliateClicks),\n}));\n\nexport const affiliateClicksRelations = relations(affiliateClicks, ({ one }) => ({\n  product: one(products, {\n    fields: [affiliateClicks.productId],\n    references: [products.id],\n  }),\n}));\n\n// Type exports for TypeScript\nexport type Product = typeof products.$inferSelect;\nexport type NewProduct = typeof products.$inferInsert;\nexport type AffiliateClick = typeof affiliateClicks.$inferSelect;\nexport type NewAffiliateClick = typeof affiliateClicks.$inferInsert;\nexport type EmailSubscriber = typeof emailSubscribers.$inferSelect;\nexport type NewEmailSubscriber = typeof emailSubscribers.$inferInsert;\nexport type AnalyticsEvent = typeof analyticsEvents.$inferSelect;\nexport type NewAnalyticsEvent = typeof analyticsEvents.$inferInsert;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAG,GAAG,OAAO;IAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAI,OAAO;IAAE;IAClD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,WAAW;QAAG,OAAO;IAAE;IACnD,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,MAAM,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACZ,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,QAAQ,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACd,gBAAgB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACtB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,aAAa,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACnB,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B;QAAE,WAAW;QAAI,OAAO;IAAE;IAClF,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,CAAC;IACxD,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACpC,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACnC,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC3C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC1C,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAG;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC1D,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;QAAE,QAAQ;IAAI,GAAG,OAAO;IACvE,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IAC9D,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,QAAQ;IAAI;IACnD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAI;IACxC,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QAAE,QAAQ;IAAI;IACrD,cAAc,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU;IACnD,gBAAgB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;AAC5B;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI,GAAG,OAAO;IACzD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACjB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAClE,iBAAiB,KAAK;IACxB,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/E,SAAS,IAAI,UAAU;YACrB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,SAAS,EAAE;aAAC;QAC3B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\n// Create a connection pool for better performance\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,\n  max: 20, // Maximum number of clients in the pool\n  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds\n  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established\n});\n\n// Create Drizzle instance with schema\nexport const db = drizzle(pool, { schema });\n\n// Export schema for use in other files\nexport * from './schema';\n\n// Helper function to test database connection\nexport async function testConnection() {\n  try {\n    const client = await pool.connect();\n    const result = await client.query('SELECT NOW()');\n    client.release();\n    console.log('✅ Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('❌ Database connection failed:', error);\n    return false;\n  }\n}\n\n// Graceful shutdown\nprocess.on('SIGINT', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', async () => {\n  console.log('Closing database pool...');\n  await pool.end();\n  process.exit(0);\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;;AAEA,kDAAkD;AAClD,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK,sCAAwC,0BAAgC;IAC7E,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO;;AAMlC,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;QAClC,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,sCAAsC,OAAO,IAAI,CAAC,EAAE;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,UAAU;IACnB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf;AAEA,QAAQ,EAAE,CAAC,WAAW;IACpB,QAAQ,GAAG,CAAC;IACZ,MAAM,KAAK,GAAG;IACd,QAAQ,IAAI,CAAC;AACf", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/api/analytics/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { db, affiliateClicks, analyticsEvents, products } from '@/lib/db';\nimport { sql, desc, count, eq } from 'drizzle-orm';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const days = parseInt(searchParams.get('days') || '30');\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - days);\n\n    // Get affiliate clicks summary\n    const clicksSummary = await db\n      .select({\n        affiliatePartner: affiliateClicks.affiliatePartner,\n        totalClicks: count(affiliateClicks.id),\n      })\n      .from(affiliateClicks)\n      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)\n      .groupBy(affiliateClicks.affiliatePartner)\n      .orderBy(desc(count(affiliateClicks.id)));\n\n    // Get top products by clicks\n    const topProducts = await db\n      .select({\n        productId: affiliateClicks.productId,\n        productName: products.name,\n        totalClicks: count(affiliateClicks.id),\n      })\n      .from(affiliateClicks)\n      .leftJoin(products, eq(affiliateClicks.productId, products.id))\n      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)\n      .groupBy(affiliateClicks.productId, products.name)\n      .orderBy(desc(count(affiliateClicks.id)))\n      .limit(10);\n\n    // Get daily clicks for chart\n    const dailyClicks = await db\n      .select({\n        date: sql<string>`DATE(${affiliateClicks.clickedAt})`,\n        clicks: count(affiliateClicks.id),\n      })\n      .from(affiliateClicks)\n      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`)\n      .groupBy(sql`DATE(${affiliateClicks.clickedAt})`)\n      .orderBy(sql`DATE(${affiliateClicks.clickedAt})`);\n\n    // Get total stats\n    const totalStats = await db\n      .select({\n        totalClicks: count(affiliateClicks.id),\n      })\n      .from(affiliateClicks)\n      .where(sql`${affiliateClicks.clickedAt} >= ${startDate}`);\n\n    // Get email subscribers count\n    const emailStats = await db\n      .select({\n        totalSubscribers: count(),\n      })\n      .from(db.select().from(db.select().from(sql`email_subscribers`)));\n\n    // Get recent clicks for activity feed\n    const recentClicks = await db\n      .select({\n        id: affiliateClicks.id,\n        affiliatePartner: affiliateClicks.affiliatePartner,\n        productName: products.name,\n        clickedAt: affiliateClicks.clickedAt,\n        utmSource: affiliateClicks.utmSource,\n        utmMedium: affiliateClicks.utmMedium,\n      })\n      .from(affiliateClicks)\n      .leftJoin(products, eq(affiliateClicks.productId, products.id))\n      .orderBy(desc(affiliateClicks.clickedAt))\n      .limit(20);\n\n    const dashboardData = {\n      summary: {\n        totalClicks: totalStats[0]?.totalClicks || 0,\n        totalSubscribers: emailStats[0]?.totalSubscribers || 0,\n        dateRange: `${startDate.toISOString().split('T')[0]} to ${new Date().toISOString().split('T')[0]}`,\n      },\n      affiliatePartners: clicksSummary,\n      topProducts: topProducts.filter(p => p.productId !== null),\n      dailyClicks,\n      recentActivity: recentClicks,\n    };\n\n    return NextResponse.json(dashboardData);\n\n  } catch (error) {\n    console.error('❌ Analytics dashboard failed:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch analytics data' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST endpoint to record custom events\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { eventType, pageUrl, eventData } = body;\n\n    if (!eventType) {\n      return NextResponse.json(\n        { error: 'Missing required field: eventType' },\n        { status: 400 }\n      );\n    }\n\n    // Get user info\n    const userAgent = request.headers.get('user-agent') || '';\n    const forwardedFor = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const userIp = forwardedFor?.split(',')[0] || realIp || request.ip || '127.0.0.1';\n\n    // Save analytics event\n    await db.insert(analyticsEvents).values({\n      eventType,\n      pageUrl: pageUrl || request.headers.get('referer') || '',\n      userIp,\n      userAgent,\n      eventData: eventData || null,\n    });\n\n    return NextResponse.json({ success: true });\n\n  } catch (error) {\n    console.error('❌ Custom event tracking failed:', error);\n    return NextResponse.json(\n      { error: 'Failed to track event' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;;;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,+BAA+B;QAC/B,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,CAAC;YACN,kBAAkB,4HAAA,CAAA,kBAAe,CAAC,gBAAgB;YAClD,aAAa,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE;QACvC,GACC,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,KAAK,CAAC,8IAAA,CAAA,MAAG,CAAC,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EACvD,OAAO,CAAC,4HAAA,CAAA,kBAAe,CAAC,gBAAgB,EACxC,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE;QAExC,6BAA6B;QAC7B,MAAM,cAAc,MAAM,2IAAA,CAAA,KAAE,CACzB,MAAM,CAAC;YACN,WAAW,4HAAA,CAAA,kBAAe,CAAC,SAAS;YACpC,aAAa,4HAAA,CAAA,WAAQ,CAAC,IAAI;YAC1B,aAAa,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE;QACvC,GACC,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,QAAQ,CAAC,4HAAA,CAAA,WAAQ,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,EAAE,4HAAA,CAAA,WAAQ,CAAC,EAAE,GAC5D,KAAK,CAAC,8IAAA,CAAA,MAAG,CAAC,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EACvD,OAAO,CAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,EAAE,4HAAA,CAAA,WAAQ,CAAC,IAAI,EAChD,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE,IACrC,KAAK,CAAC;QAET,6BAA6B;QAC7B,MAAM,cAAc,MAAM,2IAAA,CAAA,KAAE,CACzB,MAAM,CAAC;YACN,MAAM,8IAAA,CAAA,MAAG,AAAQ,CAAC,KAAK,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,QAAQ,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE;QAClC,GACC,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,KAAK,CAAC,8IAAA,CAAA,MAAG,CAAC,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EACvD,OAAO,CAAC,8IAAA,CAAA,MAAG,CAAC,KAAK,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,CAAC,CAAC,EAC/C,OAAO,CAAC,8IAAA,CAAA,MAAG,CAAC,KAAK,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,CAAC,CAAC;QAElD,kBAAkB;QAClB,MAAM,aAAa,MAAM,2IAAA,CAAA,KAAE,CACxB,MAAM,CAAC;YACN,aAAa,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,EAAE;QACvC,GACC,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,KAAK,CAAC,8IAAA,CAAA,MAAG,CAAC,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC;QAE1D,8BAA8B;QAC9B,MAAM,aAAa,MAAM,2IAAA,CAAA,KAAE,CACxB,MAAM,CAAC;YACN,kBAAkB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD;QACxB,GACC,IAAI,CAAC,2IAAA,CAAA,KAAE,CAAC,MAAM,GAAG,IAAI,CAAC,2IAAA,CAAA,KAAE,CAAC,MAAM,GAAG,IAAI,CAAC,8IAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC;QAEhE,sCAAsC;QACtC,MAAM,eAAe,MAAM,2IAAA,CAAA,KAAE,CAC1B,MAAM,CAAC;YACN,IAAI,4HAAA,CAAA,kBAAe,CAAC,EAAE;YACtB,kBAAkB,4HAAA,CAAA,kBAAe,CAAC,gBAAgB;YAClD,aAAa,4HAAA,CAAA,WAAQ,CAAC,IAAI;YAC1B,WAAW,4HAAA,CAAA,kBAAe,CAAC,SAAS;YACpC,WAAW,4HAAA,CAAA,kBAAe,CAAC,SAAS;YACpC,WAAW,4HAAA,CAAA,kBAAe,CAAC,SAAS;QACtC,GACC,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,QAAQ,CAAC,4HAAA,CAAA,WAAQ,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,EAAE,4HAAA,CAAA,WAAQ,CAAC,EAAE,GAC5D,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,SAAS,GACtC,KAAK,CAAC;QAET,MAAM,gBAAgB;YACpB,SAAS;gBACP,aAAa,UAAU,CAAC,EAAE,EAAE,eAAe;gBAC3C,kBAAkB,UAAU,CAAC,EAAE,EAAE,oBAAoB;gBACrD,WAAW,GAAG,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;YACpG;YACA,mBAAmB;YACnB,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;YACrD;YACA,gBAAgB;QAClB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;QAE1C,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACvD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,SAAS,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU,QAAQ,EAAE,IAAI;QAEtE,uBAAuB;QACvB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,kBAAe,EAAE,MAAM,CAAC;YACtC;YACA,SAAS,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;YACtD;YACA;YACA,WAAW,aAAa;QAC1B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAE3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}