{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/api/tracking/click/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { productId, affiliateUrl, source } = body;\n\n    // Get user info for tracking\n    const userAgent = request.headers.get('user-agent') || '';\n    const referrer = request.headers.get('referer') || '';\n    const forwardedFor = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';\n\n    // Create tracking data\n    const trackingData = {\n      productId,\n      affiliateUrl,\n      source,\n      userAgent,\n      referrer,\n      ipAddress: hashIP(ip), // Hash for privacy\n      timestamp: new Date().toISOString(),\n      sessionId: generateSessionId(),\n    };\n\n    // Log the click (in production, save to database)\n    console.log('Affiliate click tracked:', trackingData);\n\n    // In production, you would save this to your database\n    // await saveAffiliateClick(trackingData);\n\n    return NextResponse.json({ \n      success: true, \n      message: 'Click tracked successfully' \n    });\n\n  } catch (error) {\n    console.error('Error tracking affiliate click:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to track click' },\n      { status: 500 }\n    );\n  }\n}\n\n// Simple hash function for IP privacy\nfunction hashIP(ip: string): string {\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n// Generate simple session ID\nfunction generateSessionId(): string {\n  return Math.random().toString(36).substring(2, 15) + \n         Math.random().toString(36).substring(2, 15);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG;QAE5C,6BAA6B;QAC7B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACvD,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;QACnD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,KAAK,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU;QAEpD,uBAAuB;QACvB,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA,WAAW,OAAO;YAClB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;QACb;QAEA,kDAAkD;QAClD,QAAQ,GAAG,CAAC,4BAA4B;QAExC,sDAAsD;QACtD,0CAA0C;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,sCAAsC;AACtC,SAAS,OAAO,EAAU;IACxB,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAEA,6BAA6B;AAC7B,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACjD", "debugId": null}}]}