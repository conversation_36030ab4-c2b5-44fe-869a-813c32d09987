{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/listicles/top-products.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TopProducts = registerClientReference(\n    function() { throw new Error(\"Attempted to call TopProducts() from the server but TopProducts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/listicles/top-products.tsx <module evaluation>\",\n    \"TopProducts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/listicles/top-products.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TopProducts = registerClientReference(\n    function() { throw new Error(\"Attempted to call TopProducts() from the server but TopProducts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/listicles/top-products.tsx\",\n    \"TopProducts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/data/products.ts"], "sourcesContent": ["import { Product } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: 'organic-moringa-powder-premium',\n    name: 'Organic Moringa Powder Premium',\n    brand: 'Pure Moringa',\n    type: 'powder',\n    price: 24.99,\n    rating: 4.8,\n    pros: [\n      'USDA Organic certified',\n      'Third-party tested for purity',\n      'Rich, earthy flavor',\n      'Fine powder texture mixes well',\n      'Excellent nutrient profile',\n      'Sustainable farming practices'\n    ],\n    cons: [\n      'Slightly higher price point',\n      'Strong taste may not appeal to everyone',\n      'Packaging could be more eco-friendly'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example1',\n        price: 24.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'vitacost-link-1',\n        productId: 'organic-moringa-powder-premium',\n        retailer: 'Vitacost',\n        url: 'https://vitacost.com/example1',\n        price: 26.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-1',\n        url: '/images/products/organic-moringa-powder.svg',\n        alt: 'Organic Moringa Powder Premium package',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Non-GMO', 'Gluten-Free'],\n    description: 'Premium organic moringa leaf powder sourced from sustainable farms',\n    ingredients: ['100% Organic Moringa Oleifera Leaf Powder'],\n    servingSize: '1 teaspoon (3g)',\n    servingsPerContainer: 100,\n    thirdPartyTested: true,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    amazonASIN: 'B08EXAMPLE1',\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-capsules-500mg',\n    name: 'Moringa Capsules 500mg',\n    brand: 'Nature\\'s Way',\n    type: 'capsules',\n    price: 19.99,\n    rating: 4.6,\n    pros: [\n      'Convenient capsule form',\n      'Standardized 500mg dose',\n      'No taste or mixing required',\n      'Good value for money',\n      'Vegetarian capsules',\n      'Third-party tested'\n    ],\n    cons: [\n      'Lower concentration than powder',\n      'Contains capsule fillers',\n      'More expensive per gram of moringa'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example2',\n        price: 19.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'iherb-link-2',\n        productId: 'moringa-capsules-500mg',\n        retailer: 'iHerb',\n        url: 'https://iherb.com/example2',\n        price: 18.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-2',\n        url: '/images/products/moringa-capsules-500mg.svg',\n        alt: 'Moringa Capsules 500mg bottle',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['Non-GMO', 'Vegetarian', 'GMP Certified'],\n    description: 'Convenient moringa leaf capsules with standardized 500mg dose',\n    ingredients: ['Moringa Oleifera Leaf Powder', 'Vegetarian Capsule (Cellulose)'],\n    servingSize: '2 capsules (1000mg)',\n    servingsPerContainer: 60,\n    thirdPartyTested: true,\n    organic: false,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: true,\n    amazonASIN: 'B08EXAMPLE2',\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-leaf-tea-organic',\n    name: 'Organic Moringa Leaf Tea',\n    brand: 'Traditional Medicinals',\n    type: 'tea',\n    price: 16.99,\n    rating: 4.4,\n    pros: [\n      'Organic and fair trade',\n      'Pleasant mild flavor',\n      'Easy to prepare',\n      'Good for daily consumption',\n      'Biodegradable tea bags',\n      'Affordable option'\n    ],\n    cons: [\n      'Lower moringa concentration',\n      'Some may find taste too mild',\n      'Limited shelf life once opened'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example3',\n        price: 16.99,\n        isActive: true,\n        priority: 1\n      },\n      {\n        id: 'thrive-link-3',\n        productId: 'moringa-leaf-tea-organic',\n        retailer: 'Thrive Market',\n        url: 'https://thrivemarket.com/example3',\n        price: 15.99,\n        isActive: true,\n        priority: 2\n      }\n    ],\n    images: [\n      {\n        id: 'img-3',\n        url: '/images/products/moringa-leaf-tea.svg',\n        alt: 'Organic Moringa Leaf Tea box',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Fair Trade', 'Non-GMO'],\n    description: 'Organic moringa leaf tea bags for daily wellness routine',\n    ingredients: ['100% Organic Moringa Oleifera Leaves'],\n    servingSize: '1 tea bag',\n    servingsPerContainer: 20,\n    thirdPartyTested: false,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    createdAt: new Date('2024-02-01'),\n    updatedAt: new Date('2025-01-15')\n  }\n];\n\nexport const getProductBySlug = (slug: string): Product | undefined => {\n  return sampleProducts.find(product => \n    product.id === slug || \n    product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-') === slug\n  );\n};\n\nexport const getFeaturedProducts = (): Product[] => {\n  return sampleProducts.slice(0, 3);\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBA<PERSON>,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAW;SAAc;QAC1D,aAAa;QACb,aAAa;YAAC;SAA4C;QAC1D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAW;YAAc;SAAgB;QAC1D,aAAa;QACb,aAAa;YAAC;YAAgC;SAAiC;QAC/E,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;YAAc;SAAU;QACzD,aAAa;QACb,aAAa;YAAC;SAAuC;QACrD,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,IAAI,CAAC,CAAA,UACzB,QAAQ,EAAE,KAAK,QACf,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,SAAS;AAE/D;AAEO,MAAM,sBAAsB;IACjC,OAAO,eAAe,KAAK,CAAC,GAAG;AACjC", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/best-moringa-supplements/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { TopProducts } from '@/components/listicles/top-products';\nimport { sampleProducts } from '@/data/products';\n\nexport const metadata: Metadata = {\n  title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews & Rankings',\n  description: 'Discover the best moringa supplements of 2025. Expert-tested reviews, rankings, and buying guide for moringa powder, capsules, and tea.',\n  keywords: [\n    'best moringa supplements 2025',\n    'top moringa powder',\n    'best moringa capsules',\n    'moringa supplement reviews',\n    'moringa buying guide'\n  ],\n  openGraph: {\n    title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews',\n    description: 'Expert-tested rankings of the best moringa supplements. Compare powder, capsules, and tea forms with detailed reviews and buying advice.',\n    type: 'article',\n  },\n};\n\n// Extended product data for the top 5 list\nconst topProducts = [\n  ...sampleProducts,\n  // Add two more products to make it a top 5\n  {\n    id: 'moringa-extract-capsules-premium',\n    name: 'Moringa Extract Capsules Premium',\n    brand: 'Nutricost',\n    type: 'capsules' as const,\n    price: 22.99,\n    rating: 4.5,\n    pros: [\n      'High-potency 10:1 extract',\n      'Standardized for consistent quality',\n      'Third-party tested for purity',\n      'Excellent value for extract form',\n      'Made in GMP-certified facility'\n    ],\n    cons: [\n      'Higher price than regular capsules',\n      'May be too potent for beginners',\n      'Limited availability'\n    ],\n    affiliateLinks: [\n      {\n        id: 'amazon-link-4',\n        productId: 'moringa-extract-capsules-premium',\n        retailer: 'Amazon',\n        url: 'https://amazon.com/dp/example4',\n        price: 22.99,\n        isActive: true,\n        priority: 1\n      }\n    ],\n    images: [\n      {\n        id: 'img-4',\n        url: '/images/products/moringa-extract-capsules.svg',\n        alt: 'Moringa Extract Capsules Premium bottle',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['GMP Certified', 'Third-Party Tested'],\n    description: 'High-potency moringa extract capsules with 10:1 concentration',\n    ingredients: ['Moringa Oleifera Leaf Extract (10:1)', 'Vegetarian Capsule'],\n    servingSize: '1 capsule',\n    servingsPerContainer: 120,\n    thirdPartyTested: true,\n    organic: false,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: true,\n    createdAt: new Date('2024-03-01'),\n    updatedAt: new Date('2025-01-15')\n  },\n  {\n    id: 'moringa-powder-bulk-organic',\n    name: 'Organic Moringa Powder Bulk',\n    brand: 'Starwest Botanicals',\n    type: 'powder' as const,\n    price: 34.99,\n    rating: 4.3,\n    pros: [\n      'Excellent value for bulk quantity',\n      'USDA Organic certified',\n      'Fresh, vibrant green color',\n      'Perfect for families or heavy users',\n      'Sustainable packaging'\n    ],\n    cons: [\n      'Large quantity may expire before use',\n      'Requires proper storage',\n      'Strong earthy flavor'\n    ],\n    affiliateLinks: [\n      {\n        id: 'starwest-link-5',\n        productId: 'moringa-powder-bulk-organic',\n        retailer: 'Starwest Botanicals',\n        url: 'https://starwest-botanicals.com/example5',\n        price: 34.99,\n        isActive: true,\n        priority: 1\n      }\n    ],\n    images: [\n      {\n        id: 'img-5',\n        url: '/images/products/moringa-powder-bulk.svg',\n        alt: 'Organic Moringa Powder Bulk package',\n        width: 600,\n        height: 400,\n        isPrimary: true\n      }\n    ],\n    certifications: ['USDA Organic', 'Kosher'],\n    description: 'Bulk organic moringa powder for families and heavy users',\n    ingredients: ['100% Organic Moringa Oleifera Leaf Powder'],\n    servingSize: '1 teaspoon (3g)',\n    servingsPerContainer: 333,\n    thirdPartyTested: true,\n    organic: true,\n    nonGmo: true,\n    glutenFree: true,\n    vegan: true,\n    madeInUSA: false,\n    createdAt: new Date('2024-02-15'),\n    updatedAt: new Date('2025-01-15')\n  }\n];\n\nconst methodology = `\n<h3>Our Comprehensive Testing Process</h3>\n\n<p>We evaluated over 25 moringa supplements using a rigorous testing methodology developed by our team of nutrition experts and third-party laboratories.</p>\n\n<h4>Testing Criteria (Weighted Scoring):</h4>\n\n<ul>\n<li><strong>Quality & Purity (30%):</strong> Third-party lab testing for heavy metals, pesticides, and microbial contaminants</li>\n<li><strong>Potency & Bioavailability (25%):</strong> Nutrient content analysis and absorption testing</li>\n<li><strong>Taste & Usability (20%):</strong> Palatability, mixability, and ease of use</li>\n<li><strong>Value for Money (15%):</strong> Cost per serving and overall value proposition</li>\n<li><strong>Company Reputation (10%):</strong> Manufacturing standards, certifications, and customer service</li>\n</ul>\n\n<h4>Laboratory Testing:</h4>\n\n<p>All products underwent independent laboratory analysis for:</p>\n<ul>\n<li>Heavy metals (lead, mercury, cadmium, arsenic)</li>\n<li>Pesticide residues</li>\n<li>Microbial contaminants</li>\n<li>Nutrient content verification</li>\n<li>Adulterant screening</li>\n</ul>\n\n<h4>Expert Panel Review:</h4>\n\n<p>Our panel of certified nutritionists and health experts evaluated each product based on:</p>\n<ul>\n<li>Scientific evidence supporting health claims</li>\n<li>Appropriate dosing recommendations</li>\n<li>Quality of sourcing and manufacturing</li>\n<li>Overall safety profile</li>\n</ul>\n\n<h4>Consumer Testing:</h4>\n\n<p>We conducted a 30-day consumer trial with 50 participants to assess:</p>\n<ul>\n<li>Ease of daily use and integration</li>\n<li>Taste and palatability</li>\n<li>Perceived benefits and satisfaction</li>\n<li>Any adverse effects or concerns</li>\n</ul>\n\n<p><strong>Last Updated:</strong> January 15, 2025</p>\n<p><strong>Next Review:</strong> July 2025</p>\n`;\n\nexport default function BestMoringaSupplements() {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <TopProducts\n        products={topProducts}\n        title=\"Top 5 Best Moringa Supplements 2025\"\n        description=\"After testing 25+ moringa supplements, these are our top picks for quality, purity, and value. Updated with the latest products and testing results.\"\n        methodology={methodology}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;AACF;AAEA,2CAA2C;AAC3C,MAAM,cAAc;OACf,uHAAA,CAAA,iBAAc;IACjB,2CAA2C;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAiB;SAAqB;QACvD,aAAa;QACb,aAAa;YAAC;YAAwC;SAAqB;QAC3E,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;YACJ;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;SACD;QACD,gBAAgB;YAAC;YAAgB;SAAS;QAC1C,aAAa;QACb,aAAa;YAAC;SAA4C;QAC1D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;QAClB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDrB,CAAC;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kJAAA,CAAA,cAAW;YACV,UAAU;YACV,OAAM;YACN,aAAY;YACZ,aAAa;;;;;;;;;;;AAIrB", "debugId": null}}]}