{"name": "plant-based-vitality", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.83.0", "clsx": "^2.1.1", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "framer-motion": "^12.23.6", "nanoid": "^5.1.5", "next": "15.4.2", "next-seo": "^6.8.0", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}