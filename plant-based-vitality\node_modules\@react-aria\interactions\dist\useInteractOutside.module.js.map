{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;;;AAkB3G,SAAS,0CAAmB,KAA2B;IAC5D,IAAI,OAAC,GAAG,qBAAE,iBAAiB,cAAE,UAAU,0BAAE,sBAAsB,EAAC,GAAG;IACnE,IAAI,WAAW,CAAA,GAAA,aAAK,EAAE;QACpB,eAAe;QACf,2BAA2B;IAC7B;IAEA,IAAI,gBAAgB,CAAA,GAAA,qBAAa,EAAE,CAAC;QAClC,IAAI,qBAAqB,mCAAa,GAAG,MAAM;YAC7C,IAAI,wBACF,uBAAuB;YAEzB,SAAS,OAAO,CAAC,aAAa,GAAG;QACnC;IACF;IAEA,IAAI,yBAAyB,CAAA,GAAA,qBAAa,EAAE,CAAC;QAC3C,IAAI,mBACF,kBAAkB;IAEtB;IAEA,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,QAAQ,SAAS,OAAO;QAC5B,IAAI,YACF;QAGF,MAAM,UAAU,IAAI,OAAO;QAC3B,MAAM,iBAAiB,CAAA,GAAA,uBAAe,EAAE;QAExC,mFAAmF;QACnF,IAAI,OAAO,iBAAiB,aAAa;YACvC,IAAI,UAAU,CAAC;gBACb,IAAI,MAAM,aAAa,IAAI,mCAAa,GAAG,MACzC,uBAAuB;gBAEzB,MAAM,aAAa,GAAG;YACxB;YAEA,iDAAiD;YACjD,+DAA+D;YAC/D,8CAA8C;YAC9C,eAAe,gBAAgB,CAAC,eAAe,eAAe;YAC9D,eAAe,gBAAgB,CAAC,SAAS,SAAS;YAElD,OAAO;gBACL,eAAe,mBAAmB,CAAC,eAAe,eAAe;gBACjE,eAAe,mBAAmB,CAAC,SAAS,SAAS;YACvD;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;YAC1C,IAAI,YAAY,CAAC;gBACf,IAAI,MAAM,yBAAyB,EACjC,MAAM,yBAAyB,GAAG;qBAC7B,IAAI,MAAM,aAAa,IAAI,mCAAa,GAAG,MAChD,uBAAuB;gBAEzB,MAAM,aAAa,GAAG;YACxB;YAEA,IAAI,aAAa,CAAC;gBAChB,MAAM,yBAAyB,GAAG;gBAClC,IAAI,MAAM,aAAa,IAAI,mCAAa,GAAG,MACzC,uBAAuB;gBAEzB,MAAM,aAAa,GAAG;YACxB;YAEA,eAAe,gBAAgB,CAAC,aAAa,eAAe;YAC5D,eAAe,gBAAgB,CAAC,WAAW,WAAW;YACtD,eAAe,gBAAgB,CAAC,cAAc,eAAe;YAC7D,eAAe,gBAAgB,CAAC,YAAY,YAAY;YAExD,OAAO;gBACL,eAAe,mBAAmB,CAAC,aAAa,eAAe;gBAC/D,eAAe,mBAAmB,CAAC,WAAW,WAAW;gBACzD,eAAe,mBAAmB,CAAC,cAAc,eAAe;gBAChE,eAAe,mBAAmB,CAAC,YAAY,YAAY;YAC7D;QACF;IACF,GAAG;QAAC;QAAK;QAAY;QAAe;KAAuB;AAC7D;AAEA,SAAS,mCAAa,KAAK,EAAE,GAAG;IAC9B,IAAI,MAAM,MAAM,GAAG,GACjB,OAAO;IAET,IAAI,MAAM,MAAM,EAAE;QAChB,2DAA2D;QAC3D,MAAM,gBAAgB,MAAM,MAAM,CAAC,aAAa;QAChD,IAAI,CAAC,iBAAiB,CAAC,cAAc,eAAe,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxE,OAAO;QAET,qEAAqE;QACrE,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,gCACvB,OAAO;IAEX;IAEA,IAAI,CAAC,IAAI,OAAO,EACd,OAAO;IAGT,sFAAsF;IACtF,+FAA+F;IAC/F,yFAAyF;IACzF,sEAAsE;IACtE,OAAO,CAAC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,OAAO;AACnD", "sources": ["packages/@react-aria/interactions/src/useInteractOutside.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, useEffectEvent} from '@react-aria/utils';\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\n\nexport interface InteractOutsideProps {\n  ref: RefObject<Element | null>,\n  onInteractOutside?: (e: PointerEvent) => void,\n  onInteractOutsideStart?: (e: PointerEvent) => void,\n  /** Whether the interact outside events should be disabled. */\n  isDisabled?: boolean\n}\n\n/**\n * Example, used in components like Dialogs and Popovers so they can close\n * when a user clicks outside them.\n */\nexport function useInteractOutside(props: InteractOutsideProps): void {\n  let {ref, onInteractOutside, isDisabled, onInteractOutsideStart} = props;\n  let stateRef = useRef({\n    isPointerDown: false,\n    ignoreEmulatedMouseEvents: false\n  });\n\n  let onPointerDown = useEffectEvent((e) => {\n    if (onInteractOutside && isValidEvent(e, ref)) {\n      if (onInteractOutsideStart) {\n        onInteractOutsideStart(e);\n      }\n      stateRef.current.isPointerDown = true;\n    }\n  });\n\n  let triggerInteractOutside = useEffectEvent((e: PointerEvent) => {\n    if (onInteractOutside) {\n      onInteractOutside(e);\n    }\n  });\n\n  useEffect(() => {\n    let state = stateRef.current;\n    if (isDisabled) {\n      return;\n    }\n\n    const element = ref.current;\n    const documentObject = getOwnerDocument(element);\n\n    // Use pointer events if available. Otherwise, fall back to mouse and touch events.\n    if (typeof PointerEvent !== 'undefined') {\n      let onClick = (e) => {\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      // changing these to capture phase fixed combobox\n      // Use click instead of pointerup to avoid Android Chrome issue\n      // https://issues.chromium.org/issues/40732224\n      documentObject.addEventListener('pointerdown', onPointerDown, true);\n      documentObject.addEventListener('click', onClick, true);\n\n      return () => {\n        documentObject.removeEventListener('pointerdown', onPointerDown, true);\n        documentObject.removeEventListener('click', onClick, true);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      let onMouseUp = (e) => {\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n        } else if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      let onTouchEnd = (e) => {\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      documentObject.addEventListener('mousedown', onPointerDown, true);\n      documentObject.addEventListener('mouseup', onMouseUp, true);\n      documentObject.addEventListener('touchstart', onPointerDown, true);\n      documentObject.addEventListener('touchend', onTouchEnd, true);\n\n      return () => {\n        documentObject.removeEventListener('mousedown', onPointerDown, true);\n        documentObject.removeEventListener('mouseup', onMouseUp, true);\n        documentObject.removeEventListener('touchstart', onPointerDown, true);\n        documentObject.removeEventListener('touchend', onTouchEnd, true);\n      };\n    }\n  }, [ref, isDisabled, onPointerDown, triggerInteractOutside]);\n}\n\nfunction isValidEvent(event, ref) {\n  if (event.button > 0) {\n    return false;\n  }\n  if (event.target) {\n    // if the event target is no longer in the document, ignore\n    const ownerDocument = event.target.ownerDocument;\n    if (!ownerDocument || !ownerDocument.documentElement.contains(event.target)) {\n      return false;\n    }\n    // If the target is within a top layer element (e.g. toasts), ignore.\n    if (event.target.closest('[data-react-aria-top-layer]')) {\n      return false;\n    }\n  }\n\n  if (!ref.current) {\n    return false;\n  }\n\n  // When the event source is inside a Shadow DOM, event.target is just the shadow root.\n  // Using event.composedPath instead means we can get the actual element inside the shadow root.\n  // This only works if the shadow root is open, there is no way to detect if it is closed.\n  // If the event composed path contains the ref, interaction is inside.\n  return !event.composedPath().includes(ref.current);\n}\n"], "names": [], "version": 3, "file": "useInteractOutside.module.js.map"}