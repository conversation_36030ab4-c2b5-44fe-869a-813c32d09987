{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAKM,SAAS,0CAAgB,MAAsB,EAAE,YAAmB;IACzE,MAAM,iBAAiB,CAAA,GAAA,aAAK,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,aAAK,EAAgB;IAEtC,CAAA,GAAA,gBAAQ,EAAE;QACR,eAAe,OAAO,GAAG;QACzB,OAAO;YACL,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,WAAW,SAAS,OAAO;QAC/B,IAAI,eAAe,OAAO,EACxB,eAAe,OAAO,GAAG;aACpB,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC,KAAK,IAAM,CAAC,OAAO,EAAE,CAAC,KAAK,QAAQ,CAAC,EAAE,IAC/E;QAEF,SAAS,OAAO,GAAG;IACnB,uDAAuD;IACzD,GAAG;AACL", "sources": ["packages/@react-aria/utils/src/useUpdateEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useEffect, useRef} from 'react';\n\n// Like useEffect, but only called for updates after the initial render.\nexport function useUpdateEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useEffect(() => {\n    let prevDeps = lastDeps.current;\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!prevDeps || dependencies.some((dep, i) => !Object.is(dep, prevDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "names": [], "version": 3, "file": "useUpdateEffect.module.js.map"}