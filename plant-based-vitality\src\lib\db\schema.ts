import { pgTable, uuid, varchar, decimal, jsonb, text, timestamp, inet, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Products table - stores all Moringa product information
export const products = pgTable('products', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  brand: varchar('brand', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'powder', 'capsules', 'tea', 'oil', 'liquid'
  price: decimal('price', { precision: 10, scale: 2 }),
  rating: decimal('rating', { precision: 3, scale: 2 }),
  pros: jsonb('pros'), // Array of strings
  cons: jsonb('cons'), // Array of strings
  affiliateLinks: jsonb('affiliate_links'), // Array of affiliate link objects
  images: jsonb('images'), // Array of image objects
  certifications: jsonb('certifications'), // Array of certification strings
  description: text('description'), // Rich text content
  ingredients: jsonb('ingredients'), // Array of ingredient strings
  servingSize: varchar('serving_size', { length: 100 }),
  servingsPerContainer: decimal('servings_per_container', { precision: 10, scale: 0 }),
  thirdPartyTested: boolean('third_party_tested').default(false),
  organic: boolean('organic').default(false),
  nonGmo: boolean('non_gmo').default(false),
  glutenFree: boolean('gluten_free').default(false),
  vegan: boolean('vegan').default(false),
  madeInUSA: boolean('made_in_usa').default(false),
  amazonASIN: varchar('amazon_asin', { length: 20 }),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Affiliate clicks tracking - tracks every click on affiliate links
export const affiliateClicks = pgTable('affiliate_clicks', {
  id: uuid('id').primaryKey().defaultRandom(),
  productId: uuid('product_id').references(() => products.id),
  affiliatePartner: varchar('affiliate_partner', { length: 100 }).notNull(), // 'amazon', 'iherb', etc.
  clickId: varchar('click_id', { length: 255 }).notNull().unique(),
  userIp: inet('user_ip'),
  userAgent: text('user_agent'),
  referrer: text('referrer'),
  utmSource: varchar('utm_source', { length: 100 }),
  utmMedium: varchar('utm_medium', { length: 100 }),
  utmCampaign: varchar('utm_campaign', { length: 100 }),
  pageUrl: text('page_url'),
  clickedAt: timestamp('clicked_at').defaultNow(),
});

// Email subscribers - tracks newsletter signups
export const emailSubscribers = pgTable('email_subscribers', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  firstName: varchar('first_name', { length: 100 }),
  source: varchar('source', { length: 100 }), // 'homepage', 'review-page', etc.
  convertkitId: varchar('convertkit_id', { length: 100 }),
  subscribedAt: timestamp('subscribed_at').defaultNow(),
  unsubscribedAt: timestamp('unsubscribed_at'),
});

// Analytics events - general event tracking
export const analyticsEvents = pgTable('analytics_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventType: varchar('event_type', { length: 100 }).notNull(), // 'page_view', 'affiliate_click', 'email_signup'
  pageUrl: text('page_url'),
  userIp: inet('user_ip'),
  userAgent: text('user_agent'),
  eventData: jsonb('event_data'), // Additional event-specific data
  createdAt: timestamp('created_at').defaultNow(),
});

// Define relationships
export const productsRelations = relations(products, ({ many }) => ({
  affiliateClicks: many(affiliateClicks),
}));

export const affiliateClicksRelations = relations(affiliateClicks, ({ one }) => ({
  product: one(products, {
    fields: [affiliateClicks.productId],
    references: [products.id],
  }),
}));

// Type exports for TypeScript
export type Product = typeof products.$inferSelect;
export type NewProduct = typeof products.$inferInsert;
export type AffiliateClick = typeof affiliateClicks.$inferSelect;
export type NewAffiliateClick = typeof affiliateClicks.$inferInsert;
export type EmailSubscriber = typeof emailSubscribers.$inferSelect;
export type NewEmailSubscriber = typeof emailSubscribers.$inferInsert;
export type AnalyticsEvent = typeof analyticsEvents.$inferSelect;
export type NewAnalyticsEvent = typeof analyticsEvents.$inferInsert;
