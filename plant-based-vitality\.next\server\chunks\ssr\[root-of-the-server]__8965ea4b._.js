module.exports = {

"[project]/.next-internal/server/app/reviews/organic-moringa-powder-premium/page/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/components/reviews/product-review.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ProductReview": ()=>ProductReview
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ProductReview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ProductReview() from the server but ProductReview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/reviews/product-review.tsx <module evaluation>", "ProductReview");
}),
"[project]/src/components/reviews/product-review.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ProductReview": ()=>ProductReview
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ProductReview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ProductReview() from the server but ProductReview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/reviews/product-review.tsx", "ProductReview");
}),
"[project]/src/components/reviews/product-review.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$reviews$2f$product$2d$review$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/reviews/product-review.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$reviews$2f$product$2d$review$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/reviews/product-review.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$reviews$2f$product$2d$review$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/data/products.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getFeaturedProducts": ()=>getFeaturedProducts,
    "getProductBySlug": ()=>getProductBySlug,
    "sampleProducts": ()=>sampleProducts
});
const sampleProducts = [
    {
        id: 'organic-moringa-powder-premium',
        name: 'Organic Moringa Powder Premium',
        brand: 'Pure Moringa',
        type: 'powder',
        price: 24.99,
        rating: 4.8,
        pros: [
            'USDA Organic certified',
            'Third-party tested for purity',
            'Rich, earthy flavor',
            'Fine powder texture mixes well',
            'Excellent nutrient profile',
            'Sustainable farming practices'
        ],
        cons: [
            'Slightly higher price point',
            'Strong taste may not appeal to everyone',
            'Packaging could be more eco-friendly'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-1',
                productId: 'organic-moringa-powder-premium',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example1',
                price: 24.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'vitacost-link-1',
                productId: 'organic-moringa-powder-premium',
                retailer: 'Vitacost',
                url: 'https://vitacost.com/example1',
                price: 26.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-1',
                url: '/images/products/organic-moringa-powder.svg',
                alt: 'Organic Moringa Powder Premium package',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'USDA Organic',
            'Non-GMO',
            'Gluten-Free'
        ],
        description: 'Premium organic moringa leaf powder sourced from sustainable farms',
        ingredients: [
            '100% Organic Moringa Oleifera Leaf Powder'
        ],
        servingSize: '1 teaspoon (3g)',
        servingsPerContainer: 100,
        thirdPartyTested: true,
        organic: true,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: false,
        amazonASIN: 'B08EXAMPLE1',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2025-01-15')
    },
    {
        id: 'moringa-capsules-500mg',
        name: 'Moringa Capsules 500mg',
        brand: 'Nature\'s Way',
        type: 'capsules',
        price: 19.99,
        rating: 4.6,
        pros: [
            'Convenient capsule form',
            'Standardized 500mg dose',
            'No taste or mixing required',
            'Good value for money',
            'Vegetarian capsules',
            'Third-party tested'
        ],
        cons: [
            'Lower concentration than powder',
            'Contains capsule fillers',
            'More expensive per gram of moringa'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-2',
                productId: 'moringa-capsules-500mg',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example2',
                price: 19.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'iherb-link-2',
                productId: 'moringa-capsules-500mg',
                retailer: 'iHerb',
                url: 'https://iherb.com/example2',
                price: 18.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-2',
                url: '/images/products/moringa-capsules-500mg.svg',
                alt: 'Moringa Capsules 500mg bottle',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'Non-GMO',
            'Vegetarian',
            'GMP Certified'
        ],
        description: 'Convenient moringa leaf capsules with standardized 500mg dose',
        ingredients: [
            'Moringa Oleifera Leaf Powder',
            'Vegetarian Capsule (Cellulose)'
        ],
        servingSize: '2 capsules (1000mg)',
        servingsPerContainer: 60,
        thirdPartyTested: true,
        organic: false,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: true,
        amazonASIN: 'B08EXAMPLE2',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2025-01-15')
    },
    {
        id: 'moringa-leaf-tea-organic',
        name: 'Organic Moringa Leaf Tea',
        brand: 'Traditional Medicinals',
        type: 'tea',
        price: 16.99,
        rating: 4.4,
        pros: [
            'Organic and fair trade',
            'Pleasant mild flavor',
            'Easy to prepare',
            'Good for daily consumption',
            'Biodegradable tea bags',
            'Affordable option'
        ],
        cons: [
            'Lower moringa concentration',
            'Some may find taste too mild',
            'Limited shelf life once opened'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-3',
                productId: 'moringa-leaf-tea-organic',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example3',
                price: 16.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'thrive-link-3',
                productId: 'moringa-leaf-tea-organic',
                retailer: 'Thrive Market',
                url: 'https://thrivemarket.com/example3',
                price: 15.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-3',
                url: '/images/products/moringa-leaf-tea.svg',
                alt: 'Organic Moringa Leaf Tea box',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'USDA Organic',
            'Fair Trade',
            'Non-GMO'
        ],
        description: 'Organic moringa leaf tea bags for daily wellness routine',
        ingredients: [
            '100% Organic Moringa Oleifera Leaves'
        ],
        servingSize: '1 tea bag',
        servingsPerContainer: 20,
        thirdPartyTested: false,
        organic: true,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: false,
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date('2025-01-15')
    }
];
const getProductBySlug = (slug)=>{
    return sampleProducts.find((product)=>product.id === slug || product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-') === slug);
};
const getFeaturedProducts = ()=>{
    return sampleProducts.slice(0, 3);
};
}),
"[project]/src/app/reviews/organic-moringa-powder-premium/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>OrganicMoringaPowderReview,
    "metadata": ()=>metadata
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$reviews$2f$product$2d$review$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/reviews/product-review.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/products.ts [app-rsc] (ecmascript)");
;
;
;
const product = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sampleProducts"][0]; // Organic Moringa Powder Premium
const metadata = {
    title: `${product.name} Review - Plant Based Vitality`,
    description: `Comprehensive review of ${product.name}. Expert analysis, pros & cons, and where to buy the best organic moringa powder in 2025.`,
    keywords: [
        'moringa powder review',
        'organic moringa',
        'best moringa powder',
        'moringa supplement'
    ],
    openGraph: {
        title: `${product.name} Review - Plant Based Vitality`,
        description: `Expert review of ${product.name}. Rating: ${product.rating}/5 stars. Find out if this organic moringa powder is worth buying.`,
        type: 'article'
    }
};
const reviewContent = `
<h2>What Makes This Moringa Powder Stand Out?</h2>

<p>After testing dozens of moringa powders, the <strong>Organic Moringa Powder Premium</strong> by Pure Moringa consistently ranks among our top recommendations. This USDA Organic certified powder delivers exceptional quality and purity that sets it apart from competitors.</p>

<h3>Quality and Purity</h3>

<p>What immediately impressed us about this moringa powder is its vibrant green color and fine texture. The powder has been carefully processed to maintain maximum nutrient density while ensuring easy mixing. Third-party lab testing confirms the absence of heavy metals, pesticides, and other contaminants.</p>

<p>The organic certification isn't just a label here – you can taste the difference. The powder has a rich, earthy flavor that's characteristic of high-quality moringa, without the bitter aftertaste found in lower-grade products.</p>

<h3>Nutritional Profile</h3>

<p>Each serving (1 teaspoon) provides:</p>
<ul>
<li>Vitamin A: 18% Daily Value</li>
<li>Vitamin C: 12% Daily Value</li>
<li>Iron: 11% Daily Value</li>
<li>Calcium: 9% Daily Value</li>
<li>Potassium: 15% Daily Value</li>
<li>Complete amino acid profile</li>
</ul>

<h3>Taste and Mixability</h3>

<p>The flavor is notably clean and earthy, without the harsh bitterness that plagues many moringa powders. It mixes well in smoothies, yogurt, and even water, though we recommend starting with smaller amounts if you're new to moringa.</p>

<h3>Sourcing and Sustainability</h3>

<p>Pure Moringa sources their leaves from certified organic farms in India, where moringa trees are native. The company maintains direct relationships with farmers and follows sustainable harvesting practices that support local communities.</p>

<h2>How We Tested</h2>

<p>Our testing process included:</p>
<ul>
<li>Third-party lab analysis for purity and potency</li>
<li>Taste testing by our panel of nutrition experts</li>
<li>Mixability tests in various beverages and foods</li>
<li>Comparison with 15+ other moringa powders</li>
<li>Long-term usage evaluation over 30 days</li>
</ul>

<h2>Who Should Buy This Product?</h2>

<p>This moringa powder is ideal for:</p>
<ul>
<li>Health-conscious individuals seeking organic supplements</li>
<li>People who prioritize third-party testing and purity</li>
<li>Those willing to pay slightly more for premium quality</li>
<li>Anyone looking for a versatile superfood powder</li>
</ul>

<h2>Bottom Line</h2>

<p>The Organic Moringa Powder Premium justifies its premium price with exceptional quality, purity, and taste. While it's not the cheapest option available, the superior sourcing, organic certification, and third-party testing make it an excellent investment in your health.</p>
`;
const verdict = "An exceptional organic moringa powder that delivers on quality, purity, and taste. While slightly more expensive than competitors, the USDA Organic certification, third-party testing, and superior sourcing make it worth the investment for serious health enthusiasts.";
function OrganicMoringaPowderReview() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "container mx-auto px-4 py-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$reviews$2f$product$2d$review$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ProductReview"], {
            product: product,
            content: reviewContent,
            verdict: verdict,
            lastUpdated: "January 15, 2025"
        }, void 0, false, {
            fileName: "[project]/src/app/reviews/organic-moringa-powder-premium/page.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/reviews/organic-moringa-powder-premium/page.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/app/reviews/organic-moringa-powder-premium/page.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/reviews/organic-moringa-powder-premium/page.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8965ea4b._.js.map