module.exports = {

"[project]/.next-internal/server/app/best-moringa-supplements/page/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/components/listicles/top-products.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TopProducts": ()=>TopProducts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const TopProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TopProducts() from the server but TopProducts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/listicles/top-products.tsx <module evaluation>", "TopProducts");
}),
"[project]/src/components/listicles/top-products.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TopProducts": ()=>TopProducts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const TopProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TopProducts() from the server but TopProducts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/listicles/top-products.tsx", "TopProducts");
}),
"[project]/src/components/listicles/top-products.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$listicles$2f$top$2d$products$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/listicles/top-products.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$listicles$2f$top$2d$products$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/listicles/top-products.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$listicles$2f$top$2d$products$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/data/products.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getFeaturedProducts": ()=>getFeaturedProducts,
    "getProductBySlug": ()=>getProductBySlug,
    "sampleProducts": ()=>sampleProducts
});
const sampleProducts = [
    {
        id: 'organic-moringa-powder-premium',
        name: 'Organic Moringa Powder Premium',
        brand: 'Pure Moringa',
        type: 'powder',
        price: 24.99,
        rating: 4.8,
        pros: [
            'USDA Organic certified',
            'Third-party tested for purity',
            'Rich, earthy flavor',
            'Fine powder texture mixes well',
            'Excellent nutrient profile',
            'Sustainable farming practices'
        ],
        cons: [
            'Slightly higher price point',
            'Strong taste may not appeal to everyone',
            'Packaging could be more eco-friendly'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-1',
                productId: 'organic-moringa-powder-premium',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example1',
                price: 24.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'vitacost-link-1',
                productId: 'organic-moringa-powder-premium',
                retailer: 'Vitacost',
                url: 'https://vitacost.com/example1',
                price: 26.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-1',
                url: '/images/products/organic-moringa-powder.svg',
                alt: 'Organic Moringa Powder Premium package',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'USDA Organic',
            'Non-GMO',
            'Gluten-Free'
        ],
        description: 'Premium organic moringa leaf powder sourced from sustainable farms',
        ingredients: [
            '100% Organic Moringa Oleifera Leaf Powder'
        ],
        servingSize: '1 teaspoon (3g)',
        servingsPerContainer: 100,
        thirdPartyTested: true,
        organic: true,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: false,
        amazonASIN: 'B08EXAMPLE1',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2025-01-15')
    },
    {
        id: 'moringa-capsules-500mg',
        name: 'Moringa Capsules 500mg',
        brand: 'Nature\'s Way',
        type: 'capsules',
        price: 19.99,
        rating: 4.6,
        pros: [
            'Convenient capsule form',
            'Standardized 500mg dose',
            'No taste or mixing required',
            'Good value for money',
            'Vegetarian capsules',
            'Third-party tested'
        ],
        cons: [
            'Lower concentration than powder',
            'Contains capsule fillers',
            'More expensive per gram of moringa'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-2',
                productId: 'moringa-capsules-500mg',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example2',
                price: 19.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'iherb-link-2',
                productId: 'moringa-capsules-500mg',
                retailer: 'iHerb',
                url: 'https://iherb.com/example2',
                price: 18.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-2',
                url: '/images/products/moringa-capsules-500mg.svg',
                alt: 'Moringa Capsules 500mg bottle',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'Non-GMO',
            'Vegetarian',
            'GMP Certified'
        ],
        description: 'Convenient moringa leaf capsules with standardized 500mg dose',
        ingredients: [
            'Moringa Oleifera Leaf Powder',
            'Vegetarian Capsule (Cellulose)'
        ],
        servingSize: '2 capsules (1000mg)',
        servingsPerContainer: 60,
        thirdPartyTested: true,
        organic: false,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: true,
        amazonASIN: 'B08EXAMPLE2',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2025-01-15')
    },
    {
        id: 'moringa-leaf-tea-organic',
        name: 'Organic Moringa Leaf Tea',
        brand: 'Traditional Medicinals',
        type: 'tea',
        price: 16.99,
        rating: 4.4,
        pros: [
            'Organic and fair trade',
            'Pleasant mild flavor',
            'Easy to prepare',
            'Good for daily consumption',
            'Biodegradable tea bags',
            'Affordable option'
        ],
        cons: [
            'Lower moringa concentration',
            'Some may find taste too mild',
            'Limited shelf life once opened'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-3',
                productId: 'moringa-leaf-tea-organic',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example3',
                price: 16.99,
                isActive: true,
                priority: 1
            },
            {
                id: 'thrive-link-3',
                productId: 'moringa-leaf-tea-organic',
                retailer: 'Thrive Market',
                url: 'https://thrivemarket.com/example3',
                price: 15.99,
                isActive: true,
                priority: 2
            }
        ],
        images: [
            {
                id: 'img-3',
                url: '/images/products/moringa-leaf-tea.svg',
                alt: 'Organic Moringa Leaf Tea box',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'USDA Organic',
            'Fair Trade',
            'Non-GMO'
        ],
        description: 'Organic moringa leaf tea bags for daily wellness routine',
        ingredients: [
            '100% Organic Moringa Oleifera Leaves'
        ],
        servingSize: '1 tea bag',
        servingsPerContainer: 20,
        thirdPartyTested: false,
        organic: true,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: false,
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date('2025-01-15')
    }
];
const getProductBySlug = (slug)=>{
    return sampleProducts.find((product)=>product.id === slug || product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-') === slug);
};
const getFeaturedProducts = ()=>{
    return sampleProducts.slice(0, 3);
};
}),
"[project]/src/app/best-moringa-supplements/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>BestMoringaSupplements,
    "metadata": ()=>metadata
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$listicles$2f$top$2d$products$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/listicles/top-products.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/products.ts [app-rsc] (ecmascript)");
;
;
;
const metadata = {
    title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews & Rankings',
    description: 'Discover the best moringa supplements of 2025. Expert-tested reviews, rankings, and buying guide for moringa powder, capsules, and tea.',
    keywords: [
        'best moringa supplements 2025',
        'top moringa powder',
        'best moringa capsules',
        'moringa supplement reviews',
        'moringa buying guide'
    ],
    openGraph: {
        title: 'Top 5 Best Moringa Supplements 2025 - Expert Reviews',
        description: 'Expert-tested rankings of the best moringa supplements. Compare powder, capsules, and tea forms with detailed reviews and buying advice.',
        type: 'article'
    }
};
// Extended product data for the top 5 list
const topProducts = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sampleProducts"],
    // Add two more products to make it a top 5
    {
        id: 'moringa-extract-capsules-premium',
        name: 'Moringa Extract Capsules Premium',
        brand: 'Nutricost',
        type: 'capsules',
        price: 22.99,
        rating: 4.5,
        pros: [
            'High-potency 10:1 extract',
            'Standardized for consistent quality',
            'Third-party tested for purity',
            'Excellent value for extract form',
            'Made in GMP-certified facility'
        ],
        cons: [
            'Higher price than regular capsules',
            'May be too potent for beginners',
            'Limited availability'
        ],
        affiliateLinks: [
            {
                id: 'amazon-link-4',
                productId: 'moringa-extract-capsules-premium',
                retailer: 'Amazon',
                url: 'https://amazon.com/dp/example4',
                price: 22.99,
                isActive: true,
                priority: 1
            }
        ],
        images: [
            {
                id: 'img-4',
                url: '/images/products/moringa-extract-capsules.svg',
                alt: 'Moringa Extract Capsules Premium bottle',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'GMP Certified',
            'Third-Party Tested'
        ],
        description: 'High-potency moringa extract capsules with 10:1 concentration',
        ingredients: [
            'Moringa Oleifera Leaf Extract (10:1)',
            'Vegetarian Capsule'
        ],
        servingSize: '1 capsule',
        servingsPerContainer: 120,
        thirdPartyTested: true,
        organic: false,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: true,
        createdAt: new Date('2024-03-01'),
        updatedAt: new Date('2025-01-15')
    },
    {
        id: 'moringa-powder-bulk-organic',
        name: 'Organic Moringa Powder Bulk',
        brand: 'Starwest Botanicals',
        type: 'powder',
        price: 34.99,
        rating: 4.3,
        pros: [
            'Excellent value for bulk quantity',
            'USDA Organic certified',
            'Fresh, vibrant green color',
            'Perfect for families or heavy users',
            'Sustainable packaging'
        ],
        cons: [
            'Large quantity may expire before use',
            'Requires proper storage',
            'Strong earthy flavor'
        ],
        affiliateLinks: [
            {
                id: 'starwest-link-5',
                productId: 'moringa-powder-bulk-organic',
                retailer: 'Starwest Botanicals',
                url: 'https://starwest-botanicals.com/example5',
                price: 34.99,
                isActive: true,
                priority: 1
            }
        ],
        images: [
            {
                id: 'img-5',
                url: '/images/products/moringa-powder-bulk.svg',
                alt: 'Organic Moringa Powder Bulk package',
                width: 600,
                height: 400,
                isPrimary: true
            }
        ],
        certifications: [
            'USDA Organic',
            'Kosher'
        ],
        description: 'Bulk organic moringa powder for families and heavy users',
        ingredients: [
            '100% Organic Moringa Oleifera Leaf Powder'
        ],
        servingSize: '1 teaspoon (3g)',
        servingsPerContainer: 333,
        thirdPartyTested: true,
        organic: true,
        nonGmo: true,
        glutenFree: true,
        vegan: true,
        madeInUSA: false,
        createdAt: new Date('2024-02-15'),
        updatedAt: new Date('2025-01-15')
    }
];
const methodology = `
<h3>Our Comprehensive Testing Process</h3>

<p>We evaluated over 25 moringa supplements using a rigorous testing methodology developed by our team of nutrition experts and third-party laboratories.</p>

<h4>Testing Criteria (Weighted Scoring):</h4>

<ul>
<li><strong>Quality & Purity (30%):</strong> Third-party lab testing for heavy metals, pesticides, and microbial contaminants</li>
<li><strong>Potency & Bioavailability (25%):</strong> Nutrient content analysis and absorption testing</li>
<li><strong>Taste & Usability (20%):</strong> Palatability, mixability, and ease of use</li>
<li><strong>Value for Money (15%):</strong> Cost per serving and overall value proposition</li>
<li><strong>Company Reputation (10%):</strong> Manufacturing standards, certifications, and customer service</li>
</ul>

<h4>Laboratory Testing:</h4>

<p>All products underwent independent laboratory analysis for:</p>
<ul>
<li>Heavy metals (lead, mercury, cadmium, arsenic)</li>
<li>Pesticide residues</li>
<li>Microbial contaminants</li>
<li>Nutrient content verification</li>
<li>Adulterant screening</li>
</ul>

<h4>Expert Panel Review:</h4>

<p>Our panel of certified nutritionists and health experts evaluated each product based on:</p>
<ul>
<li>Scientific evidence supporting health claims</li>
<li>Appropriate dosing recommendations</li>
<li>Quality of sourcing and manufacturing</li>
<li>Overall safety profile</li>
</ul>

<h4>Consumer Testing:</h4>

<p>We conducted a 30-day consumer trial with 50 participants to assess:</p>
<ul>
<li>Ease of daily use and integration</li>
<li>Taste and palatability</li>
<li>Perceived benefits and satisfaction</li>
<li>Any adverse effects or concerns</li>
</ul>

<p><strong>Last Updated:</strong> January 15, 2025</p>
<p><strong>Next Review:</strong> July 2025</p>
`;
function BestMoringaSupplements() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "container mx-auto px-4 py-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$listicles$2f$top$2d$products$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TopProducts"], {
            products: topProducts,
            title: "Top 5 Best Moringa Supplements 2025",
            description: "After testing 25+ moringa supplements, these are our top picks for quality, purity, and value. Updated with the latest products and testing results.",
            methodology: methodology
        }, void 0, false, {
            fileName: "[project]/src/app/best-moringa-supplements/page.tsx",
            lineNumber: 189,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/best-moringa-supplements/page.tsx",
        lineNumber: 188,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/app/best-moringa-supplements/page.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/best-moringa-supplements/page.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__35fa46ca._.js.map