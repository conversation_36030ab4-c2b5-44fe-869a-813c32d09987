{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format price to USD currency\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\n/**\n * Generate slug from string\n */\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.substring(0, length).trim() + '...';\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Generate star rating display\n */\nexport function generateStarRating(rating: number): string {\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 !== 0;\n  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);\n\n  return (\n    '★'.repeat(fullStars) +\n    (hasHalfStar ? '☆' : '') +\n    '☆'.repeat(emptyStars)\n  );\n}\n\n/**\n * Validate email address\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate affiliate link with tracking parameters\n */\nexport function generateAffiliateLink(\n  baseUrl: string,\n  productId: string,\n  source?: string\n): string {\n  const url = new URL(baseUrl);\n  url.searchParams.set('utm_source', 'plantbasedvitality');\n  url.searchParams.set('utm_medium', 'affiliate');\n  url.searchParams.set('utm_campaign', productId);\n  if (source) {\n    url.searchParams.set('utm_content', source);\n  }\n  return url.toString();\n}\n\n/**\n * Hash IP address for privacy\n */\nexport function hashIP(ip: string): string {\n  // Simple hash function for privacy compliance\n  let hash = 0;\n  for (let i = 0; i < ip.length; i++) {\n    const char = ip.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  return hash.toString(36);\n}\n\n/**\n * Get reading time estimate\n */\nexport function getReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = text.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAKO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,QAAQ,IAAI,KAAK;AAC5C;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,SAAS,MAAM;IACnC,MAAM,aAAa,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;IAEvD,OACE,IAAI,MAAM,CAAC,aACX,CAAC,cAAc,MAAM,EAAE,IACvB,IAAI,MAAM,CAAC;AAEf;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,sBACd,OAAe,EACf,SAAiB,EACjB,MAAe;IAEf,MAAM,MAAM,IAAI,IAAI;IACpB,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc;IACnC,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB;IACrC,IAAI,QAAQ;QACV,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe;IACtC;IACA,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,OAAO,EAAU;IAC/B,8CAA8C;IAC9C,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IACA,OAAO,KAAK,QAAQ,CAAC;AACvB;AAKO,SAAS,eAAe,IAAY;IACzC,MAAM,iBAAiB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM;IAC1C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const baseStyles = cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n      {\n        // Variants\n        'bg-primary text-primary-foreground hover:bg-primary/90':\n          variant === 'default',\n        'bg-secondary text-secondary-foreground hover:bg-secondary/80':\n          variant === 'secondary',\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground':\n          variant === 'outline',\n        'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n        'text-primary underline-offset-4 hover:underline': variant === 'link',\n      },\n      {\n        // Sizes\n        'h-10 px-4 py-2': size === 'default',\n        'h-9 rounded-md px-3': size === 'sm',\n        'h-11 rounded-md px-8': size === 'lg',\n        'h-10 w-10': size === 'icon',\n      },\n      className\n    );\n\n    if (asChild) {\n      return (\n        <div className={baseStyles} {...props}>\n          {props.children}\n        </div>\n      );\n    }\n\n    return <button className={baseStyles} ref={ref} {...props} />;\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,mQACA;QACE,WAAW;QACX,0DACE,YAAY;QACd,gEACE,YAAY;QACd,kFACE,YAAY;QACd,gDAAgD,YAAY;QAC5D,mDAAmD,YAAY;IACjE,GACA;QACE,QAAQ;QACR,kBAAkB,SAAS;QAC3B,uBAAuB,SAAS;QAChC,wBAAwB,SAAS;QACjC,aAAa,SAAS;IACxB,GACA;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW;YAAa,GAAG,KAAK;sBAClC,MAAM,QAAQ;;;;;;IAGrB;IAEA,qBAAO,8OAAC;QAAO,WAAW;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAC3D;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/plantbasedvitality/plantbasedvitality/plant-based-vitality/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { StarIcon, CheckCircleIcon, ShieldCheckIcon } from '@heroicons/react/24/solid';\nimport Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-b from-background to-muted\">\n      {/* Hero Section */}\n      <section className=\"container mx-auto px-4 py-16 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-foreground mb-6\">\n            America's Most Trusted\n            <span className=\"text-primary block\">Moringa Supplement</span>\n            Reviews\n          </h1>\n          <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n            Science-backed reviews, independent testing, and personalized recommendations\n            to help you find the perfect Moringa supplement for your health goals.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/best-moringa-supplements\">\n              <Button size=\"lg\" className=\"text-lg px-8 py-3\">\n                Browse Reviews\n              </Button>\n            </Link>\n            <Link href=\"/guides/moringa-powder-vs-capsules\">\n              <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-3\">\n                Learn About Moringa\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Trust Indicators */}\n      <section className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <ShieldCheckIcon className=\"h-12 w-12 text-primary mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Independent Testing</h3>\n            <p className=\"text-muted-foreground\">\n              Every product reviewed undergoes rigorous third-party testing\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <StarIcon className=\"h-12 w-12 text-primary mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Expert Reviews</h3>\n            <p className=\"text-muted-foreground\">\n              Science-backed analysis from certified nutrition experts\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <CheckCircleIcon className=\"h-12 w-12 text-primary mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Transparent Process</h3>\n            <p className=\"text-muted-foreground\">\n              Clear methodology and FTC-compliant affiliate disclosures\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Reviews */}\n      <section className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-foreground mb-4\">\n            Featured Product Reviews\n          </h2>\n          <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n            Our most comprehensive reviews of the top-rated Moringa supplements in 2025\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {/* Sample Product Cards */}\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm text-muted-foreground\">POWDER</span>\n                <div className=\"flex items-center\">\n                  <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                  <span className=\"ml-1 text-sm font-medium\">4.8</span>\n                </div>\n              </div>\n              <CardTitle className=\"text-xl\">Organic Moringa Powder</CardTitle>\n              <CardDescription>\n                Premium organic moringa leaf powder with third-party testing\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span>Quality</span>\n                  <span className=\"font-medium\">Excellent</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Value</span>\n                  <span className=\"font-medium\">Great</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Price</span>\n                  <span className=\"font-medium\">$24.99</span>\n                </div>\n              </div>\n              <Link href=\"/reviews/organic-moringa-powder-premium\">\n                <Button className=\"w-full\">Read Full Review</Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm text-muted-foreground\">CAPSULES</span>\n                <div className=\"flex items-center\">\n                  <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                  <span className=\"ml-1 text-sm font-medium\">4.6</span>\n                </div>\n              </div>\n              <CardTitle className=\"text-xl\">Moringa Capsules 500mg</CardTitle>\n              <CardDescription>\n                Convenient capsules with standardized moringa leaf extract\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span>Quality</span>\n                  <span className=\"font-medium\">Very Good</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Value</span>\n                  <span className=\"font-medium\">Good</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Price</span>\n                  <span className=\"font-medium\">$19.99</span>\n                </div>\n              </div>\n              <Link href=\"/reviews/moringa-capsules-500mg\">\n                <Button className=\"w-full\">Read Full Review</Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm text-muted-foreground\">TEA</span>\n                <div className=\"flex items-center\">\n                  <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                  <span className=\"ml-1 text-sm font-medium\">4.4</span>\n                </div>\n              </div>\n              <CardTitle className=\"text-xl\">Moringa Leaf Tea</CardTitle>\n              <CardDescription>\n                Organic moringa tea bags for daily wellness routine\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span>Quality</span>\n                  <span className=\"font-medium\">Good</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Value</span>\n                  <span className=\"font-medium\">Excellent</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span>Price</span>\n                  <span className=\"font-medium\">$16.99</span>\n                </div>\n              </div>\n              <Link href=\"/reviews/moringa-leaf-tea-organic\">\n                <Button className=\"w-full\">Read Full Review</Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-primary text-primary-foreground py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">\n            Get Our Free Moringa Buyer's Guide\n          </h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto opacity-90\">\n            Download our comprehensive guide with expert tips, dosage recommendations,\n            and what to look for when choosing a Moringa supplement.\n          </p>\n          <Link href=\"/guides/moringa-powder-vs-capsules\">\n            <Button\n              variant=\"secondary\"\n              size=\"lg\"\n              className=\"text-lg px-8 py-3\"\n            >\n              Download Free Guide\n            </Button>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsD;8CAElE,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;gCAAyB;;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAAuD;;;;;;sCAIpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoB;;;;;;;;;;;8CAIlD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAIzD,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}